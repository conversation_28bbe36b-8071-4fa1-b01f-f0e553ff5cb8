<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Payment extends MY_Model
{

    var $id;
    var $payment_id;
    var $transaction_id;
    var $status;
    var $track_id;
    var $response_code;
    var $card_brand;
    var $response_hash;
    var $points_cnt;
    var $amount;
    var $currency;
    var $user_id;
    var $payment_type;
    var $masked_pan;
    var $invoice_file;
    var $created_at;
    var $updated_at;
    var $type;
    var $erp_id;

    public function __construct()
    {
        parent::__construct();
        $this->table_name = $this->table_names['payments'];
    }

    public function to_array()
    {
        return array(
            'id' => $this->id,
            'payment_id' => $this->payment_id,
            'transaction_id' => $this->transaction_id,
            'status' => $this->status,
            'track_id' => $this->track_id,
            'response_code' => $this->response_code,
            'card_brand' => $this->card_brand,
            'response_hash' => $this->response_hash,
            'points_cnt' => $this->points_cnt,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'user_id' => $this->user_id,
            'payment_type' => $this->payment_type,
            'masked_pan' => $this->masked_pan,
            'invoice_file' => $this->invoice_file,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'type' => $this->type,
            'erp_id' => $this->erp_id,
        );
    }


    public function get_by_id($id)
    {
        $query = $this->db->get_where($this->table_name, ['id' => $id]);
        if ($query->num_rows() > 0) {
            return $this->get_by_object($query->first_row());
        } else {
            return null;
        }
    }

    public function find_by_array($arr)
    {
        $query = $this->db->get_where($this->table_name, $arr);
        if ($query->num_rows() > 0) {
            return $this->get_by_object($query->first_row());
        } else {
            return null;
        }
    }


    public function get_all_by_user_id($user_id, $offset = -1, $limit = 1000)
    {

        //$query = $this->db->get_where($this->table_name, ['user_id'=>$user_id])
        //->result_array();

        $search = 'where user_id = ' . $user_id;
        $sql = "SELECT t1.*,t2.name,t2.username,t3.points_cnt
            FROM {$this->table_name} t1 
            INNER JOIN {$this->table_names['user']} t2 on t1.user_id = t2.id
             {$search}";

        if ($offset > -1)
            $sql .= " LIMIT {$offset}, {$limit}";

        $query = $this->db->query($sql);
        return $query->result_array();

    }


    public function get_all_by_dealer_id($dealer_id = 0, $offset = -1, $limit = 1000)
    {

        $search = 'where t2.parent_id = ' . $dealer_id;
        $sql = "SELECT t1.*,t2.name,t2.username,t3.points_cnt
            FROM {$this->table_name} t1 
            INNER JOIN {$this->table_names['user']} t2 on t1.user_id = t2.id
            INNER JOIN payment_plan t3 on t1.id = t3.payment_id
             {$search}";

        if ($offset > -1)
            $sql .= " LIMIT {$offset}, {$limit}";

        $query = $this->db->query($sql);
        //dd($query->result());
        return $query->result();

    }



    public function get_all()
    {
        return $this->db->get_where($this->table_name, [])->result_array();
    }

    // Read	
    public function load_data($params, $table_join = null)
    {
        $this->table_name = "{$this->table_name} t1 INNER JOIN user t4 on t1.user_id = t4.id ";

        if ($table_join === 'plan')
            $this->table_name .= "INNER JOIN payment_plan t2 on t1.id = t2.payment_id";

        $this->select_columns = array(
            "t1.id",
            "t1.payment_id",
            "t1.transaction_id",
            "t1.status",
            "t1.track_id",
            "t1.response_code",
            "t1.card_brand",
            "t1.response_hash",
            "t1.amount",
            "t1.currency",
            "t1.user_id",
            "t1.payment_type",
            "t1.masked_pan",
            "t1.invoice_file",
            "t1.created_at",
            "t1.updated_at",
            "t4.username",
            "t1.type",
            "t4.erp_id"
        );
        if ($table_join === 'plan')
            $this->select_columns[] = 't2.points_cnt';

        if ($table_join === 'plan-sender') {
            $this->table_name .= "left JOIN payment_plan t2 on t1.id = t2.payment_id";
            $this->select_columns[] = 't2.points_cnt';
        }

        $this->default_search = "t1.id > 0";
        if (isset($params['user_id']))
            $this->default_search .= " AND t1.user_id = '{$params['user_id']}'";

        if (isset($params['parent_id'])) {
            $this->default_search .= " AND t4.parent_id = '{$params['parent_id']}'";
        }

        if (!empty($params['username'])) {
            $this->default_search .= " AND t4.username like '%{$params['username']}%'";
        }
        if (!empty($params['points_from'])) {
            $this->default_search .= " AND t2.points_cnt >= '{$params['points_from']}'";
        }
        if (!empty($params['points_till'])) {
            $this->default_search .= " AND t2.points_cnt <= '{$params['points_till']}'";
        }
        if (!empty($params['date_from'])) {
            $this->default_search .= " AND t1.created_at >= '{$params['date_from']}%'";
        }
        if (!empty($params['date_till'])) {

            $this->default_search .= " AND t1.created_at <= '{$params['date_till']}%'";
        }


        $this->default_columns = array('id', 't1.payment_id', 'transaction_id', 'status', 'track_id', 'card_brand', 'user_id', 'created_at', 'updated_at', 'is_active', 'type','erp_id');
        $this->search_columns = array('t1.payment_id', 'card_brand', 'payment_type');
        $this->sort_columns = array('id', 'user_id', 'created_at');
        return parent::load_data($params);
    }

    public function export_data($table_join = 'plan', $from_date = null, $till_date = null, $from_points = null, $till_points = null, $username = null, $erp_id = null, $counter = null)
    {
        // Prepare parameters array for load_data
        $params = array();

        // Add filters based on provided parameters
        if (!empty($from_date)) {
            $params['date_from'] = $from_date;
        }

        if (!empty($till_date)) {
            $params['date_till'] = $till_date;
        }

        if (!empty($from_points)) {
            $params['points_from'] = $from_points;
        }

        if (!empty($till_points)) {
            $params['points_till'] = $till_points;
        }

        if (!empty($username)) {
            $params['username'] = $username;
        }

        if (!empty($erp_id)) {
            $params['parent_id'] = $erp_id;
        }


        // Add DataTables required parameters
        $params['length'] = $counter ?? 10000;
        $params['start'] = 0;
        $params['draw'] = 1;

        // Add sorting parameters
        $params['iSortCol_0'] = 0; // Sort by first column (usually ID)
        $params['sSortDir_0'] = 'desc'; // Sort direction (desc = newest first)
        $params['iSortingCols'] = 1; // Number of columns to sort
        $params['iDisplayStart'] = 0; // Start from the first record
        $params['iDisplayLength'] = $params['length']; // Set the number of records to display

        // Use the existing load_data method with the table join type
        $result = $this->load_data($params, $table_join);
      
        $data = json_decode($result);
        // Return data without pagination wrapper
        return $data->aaData;
    }

    public function get_row($row)
    {
        $res = [
            'id' => $row->id,
            'payment_id' => $row->payment_id,
            'transaction_id' => $row->transaction_id,
            'status' => $row->status,
            'track_id' => $row->track_id,
            'response_code' => $row->response_code,
            'card_brand' => $row->card_brand,
            'response_hash' => $row->response_hash,
            'amount' => $row->amount,
            'currency' => $row->currency,
            'user_id' => $row->user_id,
            'payment_type' => $row->payment_type,
            'masked_pan' => $row->masked_pan,
            'invoice_file' => $row->invoice_file,
            'created_at' => $row->created_at,
            'updated_at' => $row->updated_at,
            'points_cnt' => isset($row->points_cnt) ? $row->points_cnt : '---',
            'username' => isset($row->username) ? $row->username : '---',
            'type' => $row->type,
            'erp_id' => isset($row->erp_id) ? $row->erp_id : '---',
        ];
        return $res;
    }

}