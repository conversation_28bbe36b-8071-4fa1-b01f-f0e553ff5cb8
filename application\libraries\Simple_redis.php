<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Simple Redis Client (Fallback when Redis extension is not available)
 */
class Simple_redis {
    
    private $connection;
    private $host;
    private $port;
    private $timeout;
    
    public function __construct($config = []) {
        $this->host = isset($config['host']) ? $config['host'] : '127.0.0.1';
        $this->port = isset($config['port']) ? $config['port'] : 6379;
        $this->timeout = isset($config['timeout']) ? $config['timeout'] : 5;
        
        $this->connect();
    }
    
    private function connect() {
        $this->connection = @fsockopen($this->host, $this->port, $errno, $errstr, $this->timeout);
        
        if (!$this->connection) {
            throw new Exception("Cannot connect to Redis server: {$errstr} ({$errno})");
        }
        
        stream_set_timeout($this->connection, $this->timeout);
    }
    
    private function sendCommand($command) {
        if (!$this->connection) {
            $this->connect();
        }
        
        fwrite($this->connection, $command);
        return $this->readResponse();
    }
    
    private function readResponse() {
        $line = fgets($this->connection);
        
        if ($line === false) {
            throw new Exception("Lost connection to Redis server");
        }
        
        $type = $line[0];
        $line = substr($line, 1, -2);
        
        switch ($type) {
            case '+':
                return $line;
            case '-':
                throw new Exception("Redis error: {$line}");
            case ':':
                return (int) $line;
            case '$':
                $length = (int) $line;
                if ($length === -1) {
                    return null;
                }
                $data = fread($this->connection, $length + 2);
                return substr($data, 0, -2);
            case '*':
                $count = (int) $line;
                if ($count === -1) {
                    return null;
                }
                $array = [];
                for ($i = 0; $i < $count; $i++) {
                    $array[] = $this->readResponse();
                }
                return $array;
            default:
                throw new Exception("Unknown response type: {$type}");
        }
    }
    
    // Basic Redis commands
    public function set($key, $value, $ttl = null) {
        $command = "*3\r\n$3\r\nSET\r\n$" . strlen($key) . "\r\n{$key}\r\n$" . strlen($value) . "\r\n{$value}\r\n";
        return $this->sendCommand($command);
    }
    
    public function get($key) {
        $command = "*2\r\n$3\r\nGET\r\n$" . strlen($key) . "\r\n{$key}\r\n";
        return $this->sendCommand($command);
    }
    
    public function lPush($key, $value) {
        $command = "*3\r\n$5\r\nLPUSH\r\n$" . strlen($key) . "\r\n{$key}\r\n$" . strlen($value) . "\r\n{$value}\r\n";
        return $this->sendCommand($command);
    }
    
    public function rPop($key) {
        $command = "*2\r\n$4\r\nRPOP\r\n$" . strlen($key) . "\r\n{$key}\r\n";
        return $this->sendCommand($command);
    }
    
    public function hSet($key, $field, $value) {
        $command = "*4\r\n$4\r\nHSET\r\n$" . strlen($key) . "\r\n{$key}\r\n$" . strlen($field) . "\r\n{$field}\r\n$" . strlen($value) . "\r\n{$value}\r\n";
        return $this->sendCommand($command);
    }
    
    public function hGet($key, $field) {
        $command = "*3\r\n$4\r\nHGET\r\n$" . strlen($key) . "\r\n{$key}\r\n$" . strlen($field) . "\r\n{$field}\r\n";
        return $this->sendCommand($command);
    }
    
    public function hDel($key, $field) {
        $command = "*3\r\n$4\r\nHDEL\r\n$" . strlen($key) . "\r\n{$key}\r\n$" . strlen($field) . "\r\n{$field}\r\n";
        return $this->sendCommand($command);
    }
    
    public function zAdd($key, $score, $member) {
        $command = "*4\r\n$4\r\nZADD\r\n$" . strlen($key) . "\r\n{$key}\r\n$" . strlen($score) . "\r\n{$score}\r\n$" . strlen($member) . "\r\n{$member}\r\n";
        return $this->sendCommand($command);
    }
    
    public function zRangeByScore($key, $min, $max) {
        $command = "*4\r\n$13\r\nZRANGEBYSCORE\r\n$" . strlen($key) . "\r\n{$key}\r\n$" . strlen($min) . "\r\n{$min}\r\n$" . strlen($max) . "\r\n{$max}\r\n";
        return $this->sendCommand($command);
    }
    
    public function zRem($key, $member) {
        $command = "*3\r\n$4\r\nZREM\r\n$" . strlen($key) . "\r\n{$key}\r\n$" . strlen($member) . "\r\n{$member}\r\n";
        return $this->sendCommand($command);
    }
    
    public function exists($key) {
        $command = "*2\r\n$6\r\nEXISTS\r\n$" . strlen($key) . "\r\n{$key}\r\n";
        return $this->sendCommand($command) > 0;
    }
    
    public function del($key) {
        $command = "*2\r\n$3\r\nDEL\r\n$" . strlen($key) . "\r\n{$key}\r\n";
        return $this->sendCommand($command);
    }
    
    public function select($database) {
        $command = "*2\r\n$6\r\nSELECT\r\n$" . strlen($database) . "\r\n{$database}\r\n";
        return $this->sendCommand($command);
    }
    
    public function auth($password) {
        $command = "*2\r\n$4\r\nAUTH\r\n$" . strlen($password) . "\r\n{$password}\r\n";
        return $this->sendCommand($command);
    }
    
    public function __destruct() {
        if ($this->connection) {
            fclose($this->connection);
        }
    }
}
