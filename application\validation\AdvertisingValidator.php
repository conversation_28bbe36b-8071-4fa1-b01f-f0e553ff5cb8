<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * فئة التحقق من قواعد الإعلانات
 */
class AdvertisingValidator {
    private $CI;
    private $settings;

    public function __construct() {
        $this->CI =& get_instance();
        $this->CI->load->model('setting');
        $this->loadSettings();
    }

    private function loadSettings() {
        $this->settings = [
            'allowed_hours' => [
                'start' => '09:00:00',
                'end' => '22:00:00'
            ]
        ];
    }

    /**
     * التحقق من قواعد الإعلانات
     */
    public function validate($request, $userInfo) {
        $validateAdvertisingTiming = $this->validateAdvertisingTiming($request);
        $validateAdvertisingContent = $this->validateAdvertisingContent($request, $userInfo);
        return $validateAdvertisingTiming || $validateAdvertisingContent;
    }

    /**
     * التحقق من توقيت الإعلان
     */
    private function validateAdvertisingTiming($request) {
        if (!$this->isAdvertisingSender($request['sender_name'])) {
            return false;
        }

        if ($request['send_time_method'] === "LATER") {
            $this->validateScheduledTime($request['send_time']);
        } else {
            $this->validateCurrentTime();
        }
        return true;
    }

    /**
     * التحقق من محتوى الإعلان
     */
    private function validateAdvertisingContent($request, $userInfo) {
        $hasAdvertisingWords = $this->hasAdvertisingWords($request['message']) && 
                              !$this->isAdvertisingSender($request['sender_name']);
                              
        $hasUnauthorizedUrl = $this->hasUnauthorizedUrl($request['message'], $userInfo);

        $hasOverridNumber = !$this->isAdvertisingSender($request['sender_name']) && $this->hasOverrideNumber($request['count']);
        return $hasAdvertisingWords || $hasUnauthorizedUrl || $hasOverridNumber;
    }

    private function isAdvertisingSender($senderName) {
        return strpos($senderName, '-AD') !== false;
    }

    private function validateCurrentTime() {
        $currentTime = strtotime(server_time());
        $startTime = strtotime($this->settings['allowed_hours']['start']);
        $endTime = strtotime($this->settings['allowed_hours']['end']);

        if ($currentTime < $startTime || $currentTime > $endTime) {
            throw new Exception($this->CI->lang->line('msg_error_time_exceeded'));
        }
    }

    private function validateScheduledTime($scheduledTime) {
        $dt = new DateTime($scheduledTime);
        $time = $dt->format('H:i');
        
        if (strtotime($time) > strtotime($this->settings['allowed_hours']['end']) || 
            strtotime($time) < strtotime($this->settings['allowed_hours']['start'])) {
            throw new Exception($this->CI->lang->line('msg_error_time_exceeded'));
        }
        return true;
    }

    private function hasAdvertisingWords($message) {
        return checkAdvertisingWordsMessage($message);
    }

    private function hasUnauthorizedUrl($message, $userInfo) {
        return checkUrlInMessage($message) && $userInfo->allow_url == 0;
    }
    
    private function hasOverrideNumber($count_msg){
        $variable_sms_send_without_warning = $this->CI->setting->get_value_by_name('variable_sms_send_without_warning');
        if ($count_msg > $variable_sms_send_without_warning) {
            return true;
        }
        return false;
    }
}