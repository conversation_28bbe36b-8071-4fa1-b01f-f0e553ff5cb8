<?php

use OSS\OssClient;
use OSS\Core\OssException;

function response_json($data)
{

    $CI =& get_instance();
    $CI->output
        ->set_content_type('application/json')
        ->set_output(json_encode($data));
}


function sender_status($status)
{

    $statusMap = [
        -1 => 'disabled',
        0 => 'Inactive',
        1 => 'Active',
        2 => 'Rejected',
        3 => 'payment_watting',
        4 => 'payment_confirmation',
    ];
    $statusLabel = isset($statusMap[$status]) ? $statusMap[$status] : 'unknown';

    return $statusLabel;

}


function sender_status_trans($status)
{

    $statusMap = [
        0 => 'lbl_pending',
        1 => 'lbl_active',
        2 => 'lbl_rejected',
        3 => 'lbl_payment_watting',
        4 => 'payment_confirmation',
        -1 => 'lbl_disabled',
    ];
    $statusTranslationKey = isset($statusMap[$status]) ? $statusMap[$status] : 'unknown';
    $statusLabel = trans($statusTranslationKey);

    return $statusLabel;

}

function hlr_status($status){
    $statusMap =  [
        0 => 'lbl_blocked',
        1 => 'lbl_active'
    ];
    $statusTranslationKey = isset($statusMap[$status]) ? $statusMap[$status] : 'unknown';
    $statusLabel = trans($statusTranslationKey);

    return $statusLabel;
}


function log_event_audit($data)
{

    $CI =& get_instance();

    try {

        $CI->load->model('AuditLog');

        $default_data = [
            'event_type' => '',
            'event_description' => '',
            'entity_type' => '',
            'entity_id' => 0,
            'changes' => [],
            'created_by_id' => 0,
            'created_by_type' => '',
            'ip_address' => $CI->input->ip_address(),
            'user_agent' => $CI->input->user_agent()
        ];

        $data = array_merge($default_data, $data);
        $data['changes'] = json_encode($data['changes']);
        $data['created_at'] = date('Y-m-d H:i:s');

        return $CI->AuditLog->log_event($data);

    } catch (Exception $e) {
        // Log the error here
        log_message('error', 'Error while logging audit data: ' . $e->getMessage());
    }


}



function can_use_payment_gateway()
{
    return true;
}

if (!function_exists('can_use_plans')) {

    function can_use_plans($dealer_id = 0)
    {
        $CI =& get_instance();

        $CI->load->driver('cache', ['adapter' => 'file']);

        $user_session = $CI->session->userdata('user_logged_info');
        $cache_key = 'can_use_plans_' . $user_session['id'];

        if ($CI->cache->get($cache_key) === false) {
            $CI->load->model('user');

            if ($user_session['reseller'] == 1) {
                $dealer_id = $user_session['id'];
                $dealer = $CI->user->get_by_id($dealer_id);
                $cache_value = (bool) intval($dealer->can_use_plans);
            } else {
                $user_id = $user_session['id'];
                $user = $CI->user->get_by_id($user_id);
                $dealer = $CI->user->get_by_id($user->parent_id);
                if ($dealer) {
                    $cache_value = ($dealer->can_use_plans == 1 && $user->can_use_plans == 1);
                } else {
                    $cache_value = false;
                }

            }
            $CI->cache->save($cache_key, $cache_value, 180); // 3 minutes
        }
        return $CI->cache->get($cache_key);
    }
}




if (!function_exists('is_dealer')) {
    function is_dealer($user = null)
    {
        $CI =& get_instance();
        $user_session = $CI->session->userdata('user_logged_info');
        return ($user_session['reseller'] == 1) ? true : false;
    }
}




if (!function_exists('sender_name_count')) {

    function sender_name_count($user_id = 0)
    {
        $CI =& get_instance();

        $CI->load->driver('cache', ['adapter' => 'file']);

        $user_session = $CI->session->userdata('user_logged_info');
        $cache_key = 'sender_name_count_' . $user_session['id'];
        //$CI->cache->delete($cache_key);
        if ($CI->cache->get($cache_key) === false) {
            $CI->load->model('sender');
            $user_id = $user_session['id'];
            $senders_count = $CI->sender->count_by_user_id($user_id);
            $cache_value = $senders_count;
            $CI->cache->save($cache_key, $cache_value, 180); // 3 minutes
        }
        return $CI->cache->get($cache_key);
    }
}




if (!function_exists('uploads')) {
    function uploads($path = '')
    {
        return site_url('uploads/' . $path);
    }
}

if (!function_exists('dd')) {
    function dd()
    {
        $args = func_get_args();
        echo "<pre>";
        print_r($args);
        exit;
    }
}

if (!function_exists('trans')) {
    function trans($key)
    {
        $CI =& get_instance();
        $message = $CI->lang->line($key);
        if (!$message)
            return $key;
        return $message;
    }
}

if (!function_exists('set_session')) {
    function set_session($key, $value, $minutes = 120)
    {
        $seconds = $minutes / 60;
        $CI =& get_instance();
        $CI->session->set_userdata($key, $value, $seconds);
    }
}

if (!function_exists('get_session')) {
    function get_session($key)
    {
        $CI =& get_instance();
        return $CI->session->userdata($key);
    }
}


if (!function_exists('unset_session')) {
    function unset_session($key)
    {
        $CI =& get_instance();
        return $CI->session->unset_userdata($key);
    }
}


if (!function_exists('setFlashMessage')) {
    function setFlashMessage($key, $message)
    {
        $CI =& get_instance();
        $CI->session->set_flashdata($key, $message);
    }
}


if (!function_exists('getFlashMessage')) {
    function getFlashMessage($key = '')
    {
        $CI =& get_instance();
        $message = $CI->session->flashdata('danger');
        if ($message) {
            return "<div class='alert alert-danger alert-dismissible show' role='alert'>
           <i class='fa fa-exclamation-triangle'></i> {$message}
           </div>";
        }

        $message = $CI->session->flashdata('success');
        if ($message) {
            return "<div class='alert alert-success alert-dismissible show' role='alert'>
            {$message}
           </div>";
        }
        return false;

    }
}


function convertNumbersHindiToEn($value)
{
    $number = trim(preg_replace('/\s+/', ' ', $value));
    $persinaDigits1 = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
    $persinaDigits2 = ['٩', '٨', '٧', '٦', '٥', '٤', '٣', '٢', '١', '٠'];
    $allPersianDigits = array_merge($persinaDigits1, $persinaDigits2);
    $replaces = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    return str_replace($allPersianDigits, $replaces, $number);
}



function checkIfMobileStartCode($mobile, $country_code = '966')
{

    $mobile = convertNumbersHindiToEn($mobile);
    $mobile = str_replace(['+', '-'], '', filter_var($mobile, FILTER_SANITIZE_NUMBER_INT));

    if (strpos($mobile, "00") === 0 || strpos($mobile, "+") === 0) {
        $mobile = intval($mobile);
    }

    if (substr($mobile, 0, 6) == '966966') {
        $mobile = substr($mobile, 3, strlen($mobile));
    }

    if (!$country_code)
        $country_code = '966';

    $start_with_code = substr($mobile, 0, strlen($country_code)) === $country_code;
    if ($start_with_code)
        $mobile;
    else
        $mobile = intval($country_code) . intval($mobile);

    if (substr($mobile, 0, 6) == '966966') {
        $mobile = checkIfMobileStartCode($mobile);
    }
    return $mobile;

}

if (!function_exists('delete_file_oss')) {
    function delete_file_oss($file)
    {
        $file = explode("./", $file, 2)[1];
        $config = parse_ini_file('.env', true);
        try {
            $ossClient = new OssClient($config['s3']['OSS_ACCESS_KEY_ID'], $config['s3']['OSS_ACCESS_KEY_SECRET'], $config['s3']['OSS_ENDPOINT']);
        } catch (OssException $e) {
            print $e->getMessage();
        }

        $doesExist = $ossClient->doesObjectExist($config['s3']['OSS_BUCKET'], $file);
        if ($doesExist) {
            $result = $ossClient->deleteObject($config['s3']['OSS_BUCKET'], $file);
        }
    }
}

if (!function_exists('get_file_oss')) {
    function get_file_oss($file)
    {
        if (file_exists($file)) {
            return true;
        }
        $file = explode("./", $file, 2)[1];
        $config = parse_ini_file('.env', true);
        try {
            $ossClient = new OssClient($config['s3']['OSS_ACCESS_KEY_ID'], $config['s3']['OSS_ACCESS_KEY_SECRET'], $config['s3']['OSS_ENDPOINT']);
        } catch (OssException $e) {
            print $e->getMessage();
        }

        $doesExist = $ossClient->doesObjectExist($config['s3']['OSS_BUCKET'], $file);
        if ($doesExist) {
            $options = array(
                OssClient::OSS_FILE_DOWNLOAD => $file,
            );
            try {
                $content = $ossClient->getObject($config['s3']['OSS_BUCKET'], $file, $options);
                return true;
            } catch (OssException $e) {
                return false;
            }
        }
        return false;
    }
}

if (!function_exists('url_file_oss')) {
    function url_file_oss($file)
    {
        $file = './uploads/' . $file;
        if (file_exists($file)) {
            return base_url($file);
        }
        if (strpos($file, "./") === 0) {
            $file = explode("./", $file, 2)[1];
        }

        $config = parse_ini_file('.env', true);
        try {
            $ossClient = new OssClient($config['s3']['OSS_ACCESS_KEY_ID'], $config['s3']['OSS_ACCESS_KEY_SECRET'], $config['s3']['OSS_ENDPOINT']);
        } catch (OssException $e) {
            print $e->getMessage();
        }

        $doesExist = $ossClient->doesObjectExist($config['s3']['OSS_BUCKET'], $file);
        if ($doesExist) {

            try {
                return $ossClient->signUrl($config['s3']['OSS_BUCKET'], $file, 3600, 'GET');
            } catch (OssException $e) {
                return base_url();
            }
        }
        return base_url();
    }
}

if (!function_exists('get_calendar_url')) {
    function get_calendar_url($client_id, $summary, $description, $time, $reminder, $reminder_text, $lat, $long, $location)
    {
        $data = array(
            'client_id' => $client_id,
            'summary' => $summary,
            'description' => $description,
            'specific_time' => $time,
            'alarm' => $reminder,
            'alarm_text' => $reminder_text,
            'lat' => $lat,
            'long' => $long,
            'location' => $location
        );
        $data_string = json_encode($data);
        $curl = curl_init('https://short.dreams.sa/api/links/meeting');
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");

        curl_setopt(
            $curl,
            CURLOPT_HTTPHEADER,
            array(
                'Content-Type: application/json',
                'Content-Length: ' . strlen($data_string)
            )
        );
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);  // Make it so the data coming back is put into a string
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);  // Insert the data
        // Send the request
        $result = curl_exec($curl);
        // Free up the resources $curl is using
        curl_close($curl);
        $get_result_arr = json_decode($result, true);

        return $get_result_arr;
    }
}
function get_lat_long($google_url)
{
    $url_coordinates_position = strpos($google_url, '@') + 1;
    $coordinates = [
        "long" => null,
        "lat" => null
    ];

    if ($url_coordinates_position != false) {
        $coordinates_string = substr($google_url, $url_coordinates_position);
        $coordinates_array = explode(',', $coordinates_string);

        if (count($coordinates_array) >= 2) {
            $longitude = $coordinates_array[0];
            $latitude = $coordinates_array[1];

            $coordinates = [
                "long" => $longitude,
                "lat" => $latitude
            ];
        }

    }
    return $coordinates;
}

function extractLocationTitle($url)
{
    // Parse the URL to get the path
    $parsedUrl = parse_url($url);
    if (!isset($parsedUrl['path'])) {
        return null;
    }

    // Use a regular expression to extract the title from the path
    $path = $parsedUrl['path'];
    $pattern = '/\/place\/([^\/]+)\//';
    preg_match($pattern, $path, $matches);

    // If a match is found, decode the URL-encoded title
    if (isset($matches[1])) {
        $locationTitle = urldecode($matches[1]);
        return $locationTitle;
    }

    return null;
}

if(!function_exists('get_client_ip')){
    function get_client_ip() {
        try{
            if (array_key_exists('HTTP_X_FORWARDED_FOR', $_SERVER)) {
                return  explode(',',$_SERVER['HTTP_X_FORWARDED_FOR'])[0];
              } else if (array_key_exists('REMOTE_ADDR', $_SERVER)) {
                return $_SERVER['REMOTE_ADDR'];
              } else if (array_key_exists('HTTP_CLIENT_IP', $_SERVER)) {
                return $_SERVER['HTTP_CLIENT_IP'];
              }
            
              return '';
        }catch (Exception $ex) {
            return '';
        }
       
      }
}

function get_agent() {
    return $_SERVER['HTTP_USER_AGENT'] ?? 'UNKNOWN';
}

function getUserAgentDetails($userAgent) {
    // Convert User-Agent string to lowercase for case-insensitive comparison
    $userAgent = strtolower($userAgent);

    // Initialize variables
    $os = 'Unknown OS';
    $browser = 'Unknown Browser';
    $browserVersion = '';
    $deviceType = 'Desktop'; // Default to Desktop if not detected as mobile

    // Define patterns to match browser versions
    $browserPattern = '([^\/\s]+(?:\/[\d\.]+)?)';

    // OS detection
    if (preg_match('/android (\d+(?:\.\d+)?)/i', $userAgent, $matches)) {
        $os = 'Android ' . $matches[1];
    } elseif (preg_match('/windows nt (\d+\.\d+)/i', $userAgent, $matches)) {
        $osVersion = $matches[1];
        switch ($osVersion) {
            case '10.0':
                $os = 'Windows 10';
                break;
            case '6.3':
                $os = 'Windows 8.1';
                break;
            case '6.2':
                $os = 'Windows 8';
                break;
            case '6.1':
                $os = 'Windows 7';
                break;
            case '6.0':
                $os = 'Windows Vista';
                break;
            case '5.2':
                $os = 'Windows Server 2003/XP x64';
                break;
            case '5.1':
                $os = 'Windows XP';
                break;
            case '5.0':
                $os = 'Windows 2000';
                break;
            default:
                $os = 'Windows';
                break;
        }
    } elseif (preg_match('/macintosh|mac os x/i', $userAgent)) {
        $os = 'Macintosh';
    } elseif (preg_match('/linux/i', $userAgent)) {
        $os = 'Linux';
    }

    // Device type detection
    if (preg_match('/iphone/i', $userAgent)) {
        $deviceType = 'iPhone';
    } elseif (preg_match('/ipad/i', $userAgent)) {
        $deviceType = 'iPad';
    } elseif (preg_match('/android/i', $userAgent)) {
        $deviceType = 'Android';
    } elseif (preg_match('/blackberry/i', $userAgent)) {
        $deviceType = 'BlackBerry';
    } elseif (preg_match('/windows phone/i', $userAgent)) {
        $deviceType = 'Windows Phone';
    } elseif (preg_match('/windows mobile/i', $userAgent)) {
        $deviceType = 'Windows Mobile';
    } elseif (preg_match('/ipod/i', $userAgent)) {
        $deviceType = 'iPod';
    } elseif (preg_match('/webos/i', $userAgent)) {
        $deviceType = 'WebOS';
    }

    // Browser detection with version
    if (preg_match('/chrome\/('. $browserPattern .')/i', $userAgent, $matches)) {
        $browser = 'Chrome';
        $browserVersion = $matches[1];
    } elseif (preg_match('/safari\/('. $browserPattern .')/i', $userAgent, $matches)) {
        $browser = 'Safari';
        $browserVersion = $matches[1];
    } elseif (preg_match('/firefox\/('. $browserPattern .')/i', $userAgent, $matches)) {
        $browser = 'Firefox';
        $browserVersion = $matches[1];
    } elseif (preg_match('/edge\/('. $browserPattern .')/i', $userAgent, $matches)) {
        $browser = 'Edge';
        $browserVersion = $matches[1];
    }
    return $deviceType.'|'.$os.'<br>'.$browser.'('.$browserVersion.')';
    // Return details as an associative array
 
}

function decodeUnicodeEscape($str) {
    return preg_replace_callback('/\\\\u([0-9a-fA-F]{4})/', function ($matches) {
        return mb_convert_encoding(pack('H*', $matches[1]), 'UTF-8', 'UCS-2BE');
    }, $str);
}

function fnumber_format($number, $decimals='', $sep1='', $sep2='') {

    if (($number * pow(10 , $decimals + 1) % 10 ) == 5)  //if next not significant digit is 5
        $number -= pow(10 , -($decimals+1));

    return number_format($number, $decimals, $sep1, $sep2);

}

function checkDuplicate($message, $max_requests = 50, $sec = 60, $count = 1) {
    $CI =& get_instance();
    $CI->load->driver('cache');
   
    $cache_key = 'sms:' . md5($message); // Use MD5 to ensure the key is a valid format
    $current_time = time();

    $window_start = $current_time - $sec;
    // Retrieve existing timestamps
    $timestamps = $CI->cache->redis->get($cache_key);
   
    if ($timestamps === false) {
        $timestamps = [];
    } else {
        // Filter out timestamps outside the sliding window
        $timestamps = array_filter($timestamps, function($timestamp) use ($window_start) {
            return $timestamp >= $window_start;
        });
        

    }
   
    // Add new timestamps for the current batch
    for ($i = 0; $i < $count; $i++) {
        $timestamps[] = $current_time;
    }
   
    // Check if the total number of requests exceeds the limit
    if (count($timestamps) > $max_requests) {
        return false; // Too many requests
    }

    // Save updated timestamps
    $CI->cache->redis->save($cache_key, $timestamps, $sec);
    return true; // Within limit
}

 function is_url($url)
    {
        if (filter_var($url, FILTER_VALIDATE_URL) === false) {
            if (filter_var("http://$url", FILTER_VALIDATE_URL) === false) {
                return false;
            }
            $url = "http://$url";
        }

        $host = parse_url($url, PHP_URL_HOST);
        if (!preg_match('/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/', $host)) {
            return false;
        }

        return $url;
    }

    function asyncCurlGetWithExec($url) {
        $cmd = "curl -s -o /dev/null \"$url\" > /dev/null 2>&1 &"; // Run in background
        
    exec($cmd);

    }


    function asyncSocketGetRequest($url) {
        $urlParts = parse_url($url);
        $host = $urlParts['host'];
        $port = isset($urlParts['port']) ? $urlParts['port'] : 80;
        $path = isset($urlParts['path']) ? $urlParts['path'] : '/';
        $query = isset($urlParts['query']) ? '?' . $urlParts['query'] : '';
    
        $fp = fsockopen($host, $port, $errno, $errstr, 1); // 1 second timeout
    
        if ($fp) {
            $out = "GET $path$query HTTP/1.1\r\n";
            $out .= "Host: $host\r\n";
            $out .= "Connection: Close\r\n\r\n";
    
            fwrite($fp, $out);
            fclose($fp); // Close immediately without waiting for the response
        }
    }

    function parseNumbers($numbers){
        $all_numbers = str_replace(",", "\n", $numbers);
        $all_numbers = str_replace('"', "", $all_numbers);
        $all_numbers = explode("\n", $all_numbers);
        $all_numbers = array_filter($all_numbers, "checkIfEmpty");
        $all_numbers = array_unique($all_numbers);
        return $all_numbers;
    }

    function checkIfEmpty($value)
    {
        return strlen(trim($value)) > 0;
    }

    function checkAdvertisingWordsMessage($message)
    {
        $message_words = explode(" ", $message);
        $CI =& get_instance();
        $CI->load->model('setting');
        $site_settings = $CI->setting->get_all_as_assosiative_array();
        $advertising_words = explode(",", $site_settings['advertising_words']);
        foreach ($message_words as $word) {
            if (in_array($word, $advertising_words)) {
                return true;
            }
        }
        return false;
    }

    function checkUrlInMessage($message)
    {

        $message_words = preg_split('/\s+/', $message);
        foreach ($message_words as $word) {
            $url = checkUrl($word);
            if ($url) {
                $url = parse_url($url)['host'];
                 $CI =& get_instance();
                 $CI->load->model('whitelisturl');
                $whitelist_url = $CI->whitelisturl->get_by_url($url);
                if (!empty($whitelist_url)) {
                    continue;
                }
                return true;
            }
        }
        return false;
    }

    function checkUrl($url)
    {
        if (filter_var($url, FILTER_VALIDATE_URL) === false) {
            if (filter_var("http://$url", FILTER_VALIDATE_URL) === false) {
                return false;
            }
            $url = "http://$url";
        }

        $host = parse_url($url, PHP_URL_HOST);
        if (!preg_match('/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/', $host)) {
            return false;
        }

        return $url;
    }

    function sendTelegramMessage($message){
          $CI =& get_instance();
        $CI->curl->_simple_call("post", "https://api.telegram.org/bot8168275784:AAHkKgN-XtBHPtOYFua5t5AZxigtUweBRNU/sendMessage", ['chat_id'=>"-1002398836696",'text'=>$message], array("TIMEOUT" => 1, 'RETURNTRANSFER' => true));
    }
    
    // Example usage:
    // asyncSocketGetRequest("http://example.com/api/data?param1=value1&param2=value2");
    





