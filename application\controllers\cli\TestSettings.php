<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class TestSettings extends CI_Controller {
    
    public function __construct() {
        parent::__construct();
        
        // Make sure this can only be run from CLI
        if (!is_cli()) {
            exit("This script can only be accessed via CLI");
        }
    }

    public function excel_limit() {
        $this->load->model('setting');
        
        echo "Testing Excel Processing Limit Setting...\n";
        echo "========================================\n";
        
        // Get current setting
        $current_limit = $this->setting->get_value_by_name('excel_processing_limit');
        
        if ($current_limit) {
            echo "Current Excel Processing Limit: " . $current_limit . "\n";
        } else {
            echo "Excel Processing Limit not set (using default 10000)\n";
            
            // Set a default value
            echo "Setting default value of 10000...\n";
            $this->db->insert('setting', [
                'name' => 'excel_processing_limit',
                'value' => '10000',
                'category' => 'System',
                'caption_en' => 'Excel Processing Limit',
                'caption_ar' => 'حد معالجة ملفات Excel',
                'desc_en' => 'Maximum number of rows to process from Excel files at once',
                'desc_ar' => 'الحد الأقصى لعدد الصفوف المراد معالجتها من ملفات Excel في المرة الواحدة'
            ]);
            
            if ($this->db->affected_rows() > 0) {
                echo "✓ Default setting created successfully\n";
            } else {
                echo "✗ Failed to create default setting\n";
            }
        }
        
        echo "\nTesting in different components:\n";
        echo "-------------------------------\n";
        
        // Test in Sms controller context
        $this->load->model('setting');
        $limit_sms = $this->setting->get_value_by_name('excel_processing_limit');
        $limit_sms = !empty($limit_sms) ? (int)$limit_sms : 10000;
        echo "Sms Controller would use: " . $limit_sms . "\n";
        
        // Test in Messagedetails model context
        $limit_messagedetails = $this->setting->get_value_by_name('excel_processing_limit');
        $limit_messagedetails = !empty($limit_messagedetails) ? (int)$limit_messagedetails : 10000;
        echo "Messagedetails Model would use: " . $limit_messagedetails . "\n";
        
        echo "\n✓ Test completed successfully!\n";
    }
    
    public function update_excel_limit($new_limit = null) {
        if (empty($new_limit) || !is_numeric($new_limit)) {
            echo "Usage: php index.php cli/testsettings update_excel_limit [number]\n";
            echo "Example: php index.php cli/testsettings update_excel_limit 50000\n";
            return;
        }
        
        $this->load->model('setting');
        
        echo "Updating Excel Processing Limit to: " . $new_limit . "\n";
        
        // Update the setting
        $this->db->update('setting', 
            ['value' => $new_limit], 
            ['name' => 'excel_processing_limit']
        );
        
        if ($this->db->affected_rows() > 0) {
            echo "✓ Excel Processing Limit updated successfully!\n";
        } else {
            echo "✗ Failed to update setting or setting doesn't exist\n";
        }
        
        // Verify the update
        $updated_value = $this->setting->get_value_by_name('excel_processing_limit');
        echo "Current value: " . $updated_value . "\n";
    }
}
