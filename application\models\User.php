<?php

defined('BASEPATH') or exit('No direct script access allowed');

class User extends MY_Model
{

    // Data Members

    var $username;
    var $password;
    var $name;
    var $email;
    var $number;
    var $country_id;
    var $phone;
    var $reg_ip;
    var $address;
    var $reg_date;
    var $last_login_date;
    var $last_login_ip;
    var $blocked;
    var $active;
    var $activation_code;
    var $total_balance;
    var $spent_balance;
    var $credit_limit;
    var $balance_expire_date;
    var $can_trans_balance;
    var $unlimited_senders;
    var $reseller;
    var $parent_id;
    var $can_send_ad;
    var $admin_notification;
    var $notification_number;
    var $notification_limit;
    var $notification_status;
    var $granted_group_ids;
    var $granted_sender_ids;
    var $lang;
    var $delivery_reports;
    var $use_app;
    var $send_bloks_status;
    var $is_hidden;
    var $allow_url;
    var $delivery_status;
    var $can_use_plans;
    var $minimum_plan_price_per_sms;
    var $otp_from;
    var $secret_key;
    var $password_expiration_at;
    var $is_active_white_ip;
    var $invitation_key;
    var $is_international;
    var $linksshortcut;
    var $domain;
    var $erp_id;
    var $domIN;
    var $encrypted;
    // Related Data Members

    var $gateway_id;
    var $oauth_enabled;

    // Constructor

    public function __construct()
    {
        parent::__construct();
        $this->table_name = $this->table_names['user'];
        $this->default_search = "t1.id > 0";
    }

    // Read

    public function to_array_mini()
    {
        $this->load->library('encryption');
        return array(
            'id' => $this->id,
            'username' => $this->username,
            'country_id' => $this->country_id,
            'name' => $this->name,
            'number' => $this->number,
            'blocked' => $this->blocked,
            'active' => $this->active,
            'unlimited_senders' => $this->unlimited_senders,
            'can_trans_balance' => $this->can_trans_balance,
            'can_send_ad' => $this->can_send_ad,
            'can_use_plans' => $this->can_use_plans,
            'minimum_plan_price_per_sms' => $this->minimum_plan_price_per_sms,
            'reseller' => $this->reseller,
            'total_balance' => $this->total_balance,
            'spent_balance' => $this->spent_balance,
            'credit_limit' => $this->credit_limit,
            'balance_expire_date' => $this->balance_expire_date,
            'lang' => $this->lang,
            'otp' => $this->otp,
            'otp_from' => $this->otp_from,
            'delivery_status' => $this->delivery_status,
            'password_expiration_at' => $this->password_expiration_at,
            'secret_key' => $this->encryption->decrypt($this->secret_key),
            'is_active_white_ip' => $this->is_active_white_ip,
            'is_international' => $this->is_international,
            'linksshortcut' => $this->linksshortcut,
            'domain' => $this->domain,
            'erp_id' => $this->erp_id,
        );
    }

    public function to_array()
    {
        return array(
            'id' => $this->id,
            'username' => $this->username,
            'password' => $this->password,
            'name' => $this->name,
            'email' => $this->email,
            'number' => $this->number,
            'country_id' => $this->country_id,
            'phone' => $this->phone,
            'reg_ip' => $this->reg_ip,
            'address' => $this->address,
            'reg_date' => $this->reg_date,
            'last_login_date' => $this->last_login_date,
            'last_login_ip' => $this->last_login_ip,
            'blocked' => $this->blocked,
            'active' => $this->active,
            'activation_code' => $this->activation_code,
            'total_balance' => $this->total_balance,
            'spent_balance' => $this->spent_balance,
            'credit_limit' => $this->credit_limit,
            'balance_expire_date' => $this->balance_expire_date,
            'can_trans_balance' => $this->can_trans_balance,
            'unlimited_senders' => $this->unlimited_senders,
            'reseller' => $this->reseller,
            'parent_id' => $this->parent_id,
            'can_send_ad' => $this->can_send_ad,
            'notification_number' => $this->notification_number,
            'notification_limit' => $this->notification_limit,
            'notification_status' => $this->notification_status,
            'admin_notification' => $this->admin_notification,
            'granted_group_ids' => $this->granted_group_ids,
            'granted_sender_ids' => $this->granted_sender_ids,
            'lang' => $this->lang,
            'gateway_id' => $this->gateway_id,
            'delivery_reports' => $this->delivery_reports,
            'use_app' => $this->use_app,
            'send_bloks_status' => $this->send_bloks_status,
            'suspended_at' => isset($this->suspended_at) ? $this->suspended_at : "",
            'faild_count_login' => isset($this->faild_count_login) ? $this->faild_count_login : "",
            'is_hidden' => $this->is_hidden,
            'allow_url' => $this->allow_url,
            'delivery_status' => $this->delivery_status,
            'otp_from' => $this->otp_from,
            'secret_key' => $this->secret_key,
            'password_expiration_at' => $this->password_expiration_at,
            'can_use_plans' => $this->can_use_plans,
            'minimum_plan_price_per_sms' => $this->minimum_plan_price_per_sms,
            'is_active_white_ip' => $this->is_active_white_ip,
            'invitation_key' => $this->invitation_key,
            'is_international' => $this->is_international,
            'linksshortcut' => $this->linksshortcut,
            'domain' => $this->domain,
            'erp_id' => $this->erp_id,
            'oauth_enabled' => $this->oauth_enabled

        );
    }

    public function get_by_object($object)
    {
        $obj = parent::get_by_object($object);
        $query = $this->db->query("SELECT * FROM {$this->table_names['gateway_user']} WHERE user_id = '{$object->id}'");
        if ($query->num_rows() > 0) {
            $obj->gateway_id = $query->first_row()->gateway_id;
        }
        return $obj;
    }

    public function get_by_id($id)
    {
        /////atif 2/1/2024///////////////
        $query = $this->db->get_where($this->table_name, ['id' => $id]);

        if ($query->num_rows() > 0) {
            return $this->get_by_object($query->first_row());
        } else {
            $query = $this->db->get_where($this->table_name, ['id' => $id]);
            if ($query->num_rows() > 0) {
                return $this->get_by_object($query->first_row());
            } else {
                return null;
            }

        }
    }


    public function get_by_id_parent_id($id, $parent_id)
    {
        $query = $this->db->get_where($this->table_name, array("id" => $id, "parent_id" => $parent_id));
        if ($query->num_rows() > 0) {
            return $this->get_by_object($query->first_row());
        } else {
            return null;
        }
    }

    public function get_all_users($type = null)
    {
        $condition = ['1' => '1'];
        if ($type == 'diler') {
            $condition = ['is_hidden' => 1];
        } elseif ($type == 'school') {
            $condition = ['is_school' => 1];
        }
        $query = $this->db->select('user.id,user.username,user.number,user.email,user.reg_date,user.last_login_date,user.total_balance,user.credit_limit,user.secret_key,user.invitation_key,(select sender.side_name from sender where sender.user_id = user.id and side_name is NOT null limit 1) as side_name')
            ->select('country.name_en as country_name')
            ->join('country', 'user.country_id=country.id')
            ->where($condition)
            ->get("$this->table_name as user");

        return $query->result();
    }

    public function get_all_dealers()
    {
        $query = $this->db->select('user.id,user.username')
            ->where(['user.is_hidden' => true])
            ->get("$this->table_name");

        return $query->result_array();
    }

    public function get_all_subaccounts($parent_id)
    {
        $query = $this->db->select('user.id,user.username')
            ->where(['user.parent_id' => $parent_id])
            ->get("$this->table_name as user");

        return $query->result_array();
    }

    public function get_by_username_password($username, $password)
    {
        $query = $this->db->get_where($this->table_name, array("username" => $username, 'password' => $password));
        if ($query->num_rows() > 0) {
            return $this->get_by_object($query->first_row());
        } else {
            return null;
        }
    }

    public function get_by_username_number($username, $number)
    {
        $query = $this->db->get_where($this->table_name, array("username" => $username, "number" => $number));
        if ($query->num_rows() > 0) {
            return $this->get_by_object($query->first_row());
        } else {
            return null;
        }
    }

    public function get_by_username($username)
    {
        $query = $this->db->get_where($this->table_name, "username = '{$username}'");
        if ($query->num_rows() > 0) {
            return $this->get_by_object($query->first_row());
        } else {
            return null;
        }
    }

    public function get_by_invitation_key($invitation_key)
    {
        $query = $this->db->get_where($this->table_name, "invitation_key = '{$invitation_key}'");
        if ($query->num_rows() > 0) {
            return $this->get_by_object($query->first_row());
        } else {
            return null;
        }
    }

    public function get_by_username_like($username = '')
    {
        return $this->db->query("SELECT username FROM {$this->table_name} WHERE username like '%{$username}%' ")->result_array();
    }

    public function get_by_basic_info_like($query = '')
    {
        return $this->db->query(
            "SELECT username " .
            "FROM {$this->table_name} " .
            "WHERE username like '%{$query}%' " .
            "OR name like '%{$query}%' " .
            "OR email like '%{$query}%' " .
            "OR number like '%{$query}%' "
        )->result_array();
    }

    public function get_by_email($email)
    {
        $query = $this->db->get_where($this->table_name, "email = '{$email}'");
        if ($query->num_rows() > 0) {
            return $this->get_by_object($query->first_row());
        } else {
            return null;
        }
    }

    public function get_by_number($number)
    {
        $query = $this->db->get_where($this->table_name, "number = '{$number}'");
        if ($query->num_rows() > 0) {
            return $this->get_by_object($query->first_row());
        } else {
            return null;
        }
    }

    public function get_notified()
    {
        $res = $this->db->query(
            "SELECT * from {$this->table_name} " .
            "WHERE notification_status = 1 AND notification_has_sent = 0 AND (total_balance-spent_balance) <= notification_limit"
        )->result();

        $this->db->query(
            "UPDATE {$this->table_name} " .
            "SET notification_has_sent = 1 " .
            "WHERE notification_status = 1 AND notification_has_sent = 0 AND (total_balance-spent_balance) <= notification_limit"
        );

        return $res;
    }

    public function load_data($params)
    {
        //LEFT OUTER JOIN {$this->table_names['country']} t3 on t1.country_id = t3.id
        //
        //LEFT OUTER JOIN {$this->table_names['gateway_user']} t4 on t1.id = t4.user_id
        //LEFT OUTER JOIN {$this->table_names['gateway']} t5 on t4.gateway_id = t5.id 
        //LEFT OUTER JOIN (SELECT user_id, (-1*SUM(amount)) debit_amount FROM {$this->table_names['credit_debit']} GROUP BY user_id) t6 on t6.user_id = t1.id

        $this->table_name = "{$this->table_name} t1 
        LEFT OUTER JOIN {$this->table_name} t2 on t1.parent_id = t2.id
            ";

        $this->select_columns = array("t1.id", "t1.username", "t1.name", "t1.email", "t1.number", "'Saudi Arabia'country_en", " 'السعودية'country_ar", "t1.reg_date", "t1.last_login_date", "t1.total_balance", "t1.spent_balance", "(t1.total_balance-t1.spent_balance) balance", "t1.balance_expire_date", "t1.active", "t1.blocked", "t1.reseller", "t1.suspended_at", "t2.username as parent");//"t5.name gateway_name", "t6.debit_amount",
        $this->search_columns = array("t1.username", "t1.email", "t1.name", "t1.number");//, "t1.name", "t1.email", "t1.number"
        $this->sort_columns = array("t1.id"); //, "t2.username", "t5.name", "t1.username", "t1.reg_date", "t1.last_login_date", "(t1.total_balance-t1.spent_balance)", "t1.balance_expire_date", "t6.debit_amount"
        // Advanced Search First Row
        if (!empty($params['username'])) {
            $this->default_search .= " AND t1.username like '%{$params['username']}%'";
        }
        if (!empty($params['name'])) {
            $this->default_search .= " AND t1.name like '%{$params['name']}%'";
        }
        if (!empty($params['email'])) {
            $this->default_search .= " AND t1.email like '%{$params['email']}%'";
        }
        if (!empty($params['mobile'])) {
            $this->default_search .= " AND t1.number = '{$params['mobile']}'";
        }
        if (!empty($params['country_id'])) {
            $this->default_search .= " AND t1.country_id = '{$params['country_id']}'";
        }
        // if (!empty($params['parent'])) {
        //     $this->default_search .= " AND t2.username = '{$params['parent']}'";
        // }
        // Advanced Search Second Row
        if (!empty($params['register_from_date'])) {
            ///2024/1/4
            $this->date_filters['register_from_date'] = $params['register_from_date'];

            $this->default_search .= " AND t1.reg_date >= '{$params['register_from_date']}'";
        }
        if (!empty($params['register_till_date'])) {

            $this->date_filters['register_till_date'] = $params['register_till_date'];

            $this->default_search .= " AND t1.reg_date <= '{$params['register_till_date']}'";
        }
        if (!empty($params['lastlogin_from_date'])) {

            $this->date_filters['lastlogin_from_date'] = $params['lastlogin_from_date'];

            $this->default_search .= " AND t1.last_login_date >= '{$params['lastlogin_from_date']}'";
        }
        if (!empty($params['lastlogin_till_date'])) {

            $this->date_filters['lastlogin_till_date'] = $params['lastlogin_till_date'];

            $this->default_search .= " AND t1.last_login_date <= '{$params['lastlogin_till_date']}'";
        }
        if (!empty($params['balanceexpiration_from_date'])) {

            $this->date_filters['balanceexpiration_from_date'] = $params['balanceexpiration_from_date'];

            $this->default_search .= " AND t1.balance_expire_date >= '{$params['balanceexpiration_from_date']}'";
        }
        if (!empty($params['balanceexpiration_till_date'])) {
            $this->date_filters['balanceexpiration_till_date'] = $params['balanceexpiration_till_date'];

            $this->default_search .= " AND t1.balance_expire_date <= '{$params['balanceexpiration_till_date']}'";
        }
        // Advanced Search Third Row
        if (!empty($params['balance_from']) && is_numeric($params['balance_from'])) {
            $this->default_search .= " AND (t1.total_balance - t1.spent_balance) >= '{$params['balance_from']}'";
        }
        if (!empty($params['balance_till']) && is_numeric($params['balance_till'])) {
            $this->default_search .= " AND (t1.total_balance - t1.spent_balance) <= '{$params['balance_till']}'";
        }
        if (!empty($params['credit_from']) && is_numeric($params['credit_from'])) {
            $this->default_search .= " AND t1.credit_limit >= '{$params['credit_from']}'";
        }
        if (!empty($params['credit_till']) && is_numeric($params['credit_till'])) {
            $this->default_search .= " AND t1.credit_limit <= '{$params['credit_till']}'";
        }
        if (!empty($params['debit_from']) && is_numeric($params['debit_from'])) {
            $this->default_search .= " AND t6.debit_amount >= '{$params['debit_from']}'";
        }
        if (!empty($params['debit_till']) && is_numeric($params['debit_till'])) {
            $this->default_search .= " AND t6.debit_amount <= '{$params['debit_till']}'";
        }
        // Advanced Search Fourth Row
        // if (!empty($params['gateway_id'])) {
        //     $this->default_search .= " AND t4.gateway_id = '{$params['gateway_id']}'";
        // }
        if (isset($params['active']) && ($params['active'] >= 0)) {
            $this->default_search .= " AND t1.active = '{$params['active']}'";
        }
        if (isset($params['blocked']) && ($params['blocked'] >= 0)) {
            $this->default_search .= " AND t1.blocked = '{$params['blocked']}'";
        }
        if (isset($params['reseller']) && ($params['reseller'] >= 0)) {
            $this->default_search .= " AND t1.reseller = '{$params['reseller']}'";
        }
        if (isset($params['can_send_ad']) && ($params['can_send_ad'] >= 0)) {
            $this->default_search .= " AND t1.can_send_ad = '{$params['can_send_ad']}'";
        }
        if (isset($params['unlimited_senders']) && ($params['unlimited_senders'] >= 0)) {
            $this->default_search .= " AND t1.unlimited_senders = '{$params['unlimited_senders']}'";
        }
        if (isset($params['is_diler']) && $params['is_diler'] == 0) {
            $this->default_search .= " AND t1.is_hidden = 0 ";
        }

        if (isset($params['is_diler']) && $params['is_diler'] == 1) {
            $this->default_search .= " AND t1.is_hidden = 1 ";
        }
        if (isset($params['is_school']) && $params['is_school'] == 1) {
            $this->default_search .= " AND t1.is_school = 1 ";
        }
        // var_dump( parent::load_data($params));
        // die();
        return parent::load_data($params);
    }

    public function load_data_subaccounts($parent_id, $params)
    {
        $this->user->default_search = " parent_id = '" . $parent_id . "' ";
        $this->search_columns = array("username", "name", "number");
        $this->sort_columns = array("id", "username", "name", "number", "total_balance", "spent_balance");
        return parent::load_data($params);
    }

    public function get_row($row)
    {
        $res = array(
            'id' => $row->id,
            'username' => isset($row->username) ? $row->username : "",
            'email' => isset($row->email) ? $row->email : "",
            'number' => isset($row->number) ? $row->number : "",
            'country_en' => isset($row->country_en) ? $row->country_en : "",
            'country_ar' => isset($row->country_ar) ? $row->country_ar : "",
            'reg_date' => isset($row->reg_date) ? $row->reg_date : "",
            'last_login_date' => isset($row->last_login_date) ? $row->last_login_date : "",
            'total_balance' => isset($row->total_balance) ? $row->total_balance : "",
            'spent_balance' => isset($row->spent_balance) ? $row->spent_balance : "",
            'balance' => isset($row->balance) ? round($row->balance, 2) : "",
            'balance_expire_date' => isset($row->balance_expire_date) ? $row->balance_expire_date : "",
            'gateway_name' => isset($row->gateway_name) ? $row->gateway_name : "",
            'parent' => isset($row->parent) ? $row->parent : "",
            'active' => isset($row->active) ? $row->active : "",
            'blocked' => isset($row->blocked) ? $row->blocked : "",
            'reseller' => isset($row->reseller) ? $row->reseller : "",
            'name' => isset($row->name) ? $row->name : "",
            'debit_amount' => isset($row->debit_amount) ? $row->debit_amount : "",
            'is_active_white_ip' => isset($row->is_active_white_ip) ? $row->is_active_white_ip : 0,
            'suspended_at' => isset($row->suspended_at) ? $row->suspended_at : null,
            'is_international' => isset($row->is_international) ? $row->is_international : 0,
            'linksshortcut' => isset($row->linksshortcut) ? $row->linksshortcut : 0,
            'domain' => isset($row->domain) ? $row->domain : "",
            'can_use_plans' => isset($row->can_use_plans) ? $row->can_use_plans : 0,
            'encrypted' => isset($row->encrypted) ? $row->encrypted : 0
        );
        return $res;
    }

    public function get_contacts($offset, $limit)
    {
        $this->load->model('contact');
        return $this->contact->get_by_user_id($this->id, $offset, $limit);
    }

    public function get_contacts_like($var, $offset, $limit)
    {
        $this->load->model('contact');
        return $this->contact->get_by_user_id_like($this->id, $var, $offset, $limit);
    }

    public function get_contact_groups()
    {
        $this->load->model('contactgroup');
        return $this->contactgroup->get_by_user_id($this->id);
    }

    public function get_granted_contact_groups()
    {
        $res = null;
        if (!empty($this->parent_id) && !empty($this->granted_group_ids)) {
            $this->load->model('contactgroup');
            $res = $this->contactgroup->get_by_ids_user_id(explode(',', $this->granted_group_ids), $this->parent_id);
        }
        return $res;
    }

    public function get_unclassified_contacts_cnt()
    {
        $this->load->model('contact');
        return $this->contact->get_unclassified_cnt_by_user_id($this->id);
    }

    public function get_all_usernames_except($id)
    {
        return $this->db->query("SELECT id, username FROM {$this->table_name} WHERE id <> {$id}")->result_array();
    }

    public function get_all_emails()
    {
        return $this->db->query("SELECT email FROM {$this->table_name}")->result_array();
    }

    public function get_emails_by_ids($ids)
    {
        return $this->db->query("SELECT email FROM {$this->table_name} WHERE id in (" . implode(',', $ids) . ")")->result_array();
    }

    public function get_all_numbers()
    {
        return $this->db->query("SELECT number FROM {$this->table_name}")->result_array();
    }

    public function temp($id)
    {
        return $this->db->query("select * from `message_details` where `message_id` = " . $id . " and smpp_response='BLOCKED'")->result_array();//`smpp_response_v2` is null;
    }

    public function get_numbers_by_ids($ids)
    {
        return $this->db->query("SELECT number FROM {$this->table_name} WHERE id in (" . implode(',', $ids) . ")")->result_array();
    }

    public function check_username_availability($username, $excluded_id = 0)
    {
        $query = $this->db->get_where(
            $this->table_name,
            " username = '{$username}' AND id <> '" . (empty($excluded_id) ? "-1" : $excluded_id) . "'"
        );
        if ($query->num_rows() > 0) {
            return false;
        } else {
            return true;
        }
    }

    public function check_email_availability($email, $excluded_id = 0)
    {
        $query = $this->db->get_where(
            $this->table_name,
            " email = '{$email}' AND id <> '" . (empty($excluded_id) ? "-1" : $excluded_id) . "'"
        );
        if ($query->num_rows() > 0) {
            return false;
        } else {
            return true;
        }
    }

    public function check_number_availability($number, $excluded_id = 0)
    {
        $query = $this->db->get_where(
            $this->table_name,
            " number = '{$number}' AND id <> '" . (empty($excluded_id) ? "-1" : $excluded_id) . "'"
        );
        if ($query->num_rows() > 0) {
            return false;
        } else {
            return true;
        }
    }

    public function get_favorite_messages()
    {
        $this->load->model('favorite');
        return $this->favorite->get_by_user_id($this->id);
    }

    public function get_inactive_cnt()
    {
        $query = $this->db->query("SELECT COUNT(*) cnt FROM {$this->table_name} WHERE active = 0");
        return $query->first_row()->cnt;
    }

    public function get_blocked_cnt()
    {
        $query = $this->db->query("SELECT COUNT(*) cnt FROM {$this->table_name} WHERE blocked = 1");
        return $query->first_row()->cnt;
    }

    public function get_mobile_cnt()
    {
        $query = $this->db->query("SELECT COUNT(*) cnt FROM {$this->table_name} WHERE use_app = 1");
        return $query->first_row()->cnt;
    }

    public function get_balance_amt()
    {

        $entry = $this->db->query(
            "SELECT ROUND(SUM(total_balance)) total, " .
            "ROUND(SUM(spent_balance)) spent, " .
            "ROUND(SUM(total_balance-spent_balance)) net " .
            "FROM {$this->table_name} where is_hidden=0"
        )->first_row();
        return array('total' => $entry->total, 'spent' => $entry->spent, 'net' => $entry->net);
    }

    public function get_almost_expired()
    {
        $query = $this->db->query("SELECT * FROM {$this->table_name} WHERE balance_expire_date > NOW() ORDER BY balance_expire_date LIMIT 0, 5");
        return $query->result_array();
    }

    public function get_latest_login()
    {
        $query = $this->db->query("SELECT * FROM {$this->table_name} where is_hidden=0 ORDER BY last_login_date DESC LIMIT 0, 10");
        return $query->result_array();
    }

    public function get_latest_register()
    {
        $query = $this->db->query("SELECT * FROM {$this->table_name} where is_hidden=0 ORDER BY reg_date DESC LIMIT 0, 10");
        return $query->result_array();
    }

    public function get_negative_balance_cnt_amt()
    {
        $query = $this->db->query("SELECT ROUND(SUM(total_balance-spent_balance)) amt, count(*) cnt FROM {$this->table_name} WHERE (total_balance-spent_balance) < 0")->first_row();
        return array('amt' => $query->amt, 'cnt' => $query->cnt);
    }

    public function get_active_senders()
    {
        $this->load->model('sender');
        return $this->sender->get_active_by_user_id($this->id);
    }

    public function get_granted_senders()
    {
        $res = null;
        if (!empty($this->parent_id) && !empty($this->granted_sender_ids)) {
            $this->load->model('sender');
            $res = $this->sender->get_by_ids_user_id(explode(',', $this->granted_sender_ids), $this->parent_id);
        }
        return $res;
    }

    public function get_senders()
    {
        $this->load->model('sender');
        return $this->sender->get_by_user_id($this->id);
    }

    public function get_subaccounts()
    {
        $res = array();
        $subaccounts = $this->db->get_where($this->table_name, array('parent_id' => $this->id))->result();
        foreach ($subaccounts as $subaccount) {
            $res[] = $this->get_by_object($subaccount);
        }
        return $res;
    }

    public function get_subaccounts_by_ids($ids)
    {
        $res = array();
        $subaccounts = $this->db->get_where($this->table_name, "parent_id in (" . implode(',', $ids) . ")")->result();
        foreach ($subaccounts as $subaccount) {
            $res[] = $this->get_by_object($subaccount);
        }
        return $res;
    }

    public function get_expired_date($points_cnt)
    {
        $expired_date = date('Y-m-d', strtotime(' + 1 years'));
        $temp_point = 0;
        $balance_logs = $this->db->query("SELECT * FROM balance_log WHERE user_id = $this->id  and proccess_balance_expire_date = 0 and points_cnt > 0 and balance_expire_date > now()   order by balance_expire_date,id asc ")->result_array();
        foreach ($balance_logs as $balance_log) {
            echo $balance_log['points_cnt'] - $balance_log['points_spent'] . "," . $temp_point . "|";

            $expired_date = $balance_log['balance_expire_date'];
            $temp_point += $balance_log['points_cnt'] - $balance_log['points_spent'];
            if ($points_cnt <= $temp_point) {

                break;
            }
        }

        return $expired_date ?? date('Y-m-d', strtotime(' + 1 years'));
    }



    // Update

    public function activate_deactivate()
    {
        $this->update_by_array(array('active' => abs(1 - $this->active)), array('id' => $this->id));
    }

    public function flip_reseller()
    {
        $this->update_by_array(array('reseller' => abs(1 - $this->reseller)), array('id' => $this->id));
    }

    public function block_unblock()
    {
        $this->update_by_array(array('blocked' => abs(1 - $this->blocked)), array('id' => $this->id));
    }

    public function add_to_gateway($gateway_id)
    {
        $this->db->query("DELETE FROM {$this->table_names['gateway_user']} WHERE user_id = '{$this->id}'");
        if (!empty($gateway_id)) {
            $this->db->insert(
                $this->table_names['gateway_user'],
                array("user_id" => $this->id, "gateway_id" => $gateway_id)
            );
        }
    }

    public function change_balance($points_count, $reason, $balance_expire_date = null, $created_by = null, $amount = 0)
    {

        $this->total_balance += $points_count;
        if ($points_count > 0 && ($this->total_balance - $this->spent_balance) > $this->notification_limit) {
            $this->db->query("UPDATE {$this->table_name} SET notification_has_sent = 0, total_balance = total_balance + {$points_count} WHERE id = {$this->id}");
        } else {
            $this->db->query("UPDATE {$this->table_name} SET total_balance = total_balance + {$points_count} WHERE id = {$this->id}");
        }
        $this->load->model('balancelog');
        $id = $this->balancelog->insert_by_array(array('user_id' => $this->id, 'points_cnt' => $points_count, 'amount' => $amount ?? 0, 'reason' => $reason, 'balance_expire_date' => $balance_expire_date, 'created_by' => $created_by));
        if ($this->db->affected_rows() > 0) {
            if ($points_count < 0) {
                $balance_logs = $this->db->query("SELECT * FROM balance_log WHERE user_id = $this->id and balance_expire_date >= NOW() and proccess_balance_expire_date = 0  order by balance_expire_date,id asc ")->result_array();

                $still_amount = abs($points_count);
                foreach ($balance_logs as $balance_log) {
                    // get first charge still active
                    if ($balance_log['points_spent'] < $balance_log['points_cnt']) {
                        $available_points = $balance_log['points_cnt'] - $balance_log['points_spent'];
                        if ($available_points > $still_amount) {

                            $this->balancelog->update_by_array(array('points_spent' => $still_amount + $balance_log['points_spent'], 'proccess_balance_expire_date' => $available_points == $still_amount), array('id' => $balance_log['id']));
                            break;
                        } else {
                            $still_amount = $still_amount - $available_points;
                            $this->balancelog->update_by_array(array('points_spent' => $available_points + $balance_log['points_spent'], 'proccess_balance_expire_date' => 1), array('id' => $balance_log['id']));
                        }
                    }
                }
            }
            return $id;
        } else {
            return false;
        }
    }

    public function change_balance_without_balance_log($amount, $reason, $balance_expire_date = null, $created_by = null)
    {

        $this->total_balance += $amount;
        if ($amount > 0 && ($this->total_balance - $this->spent_balance) > $this->notification_limit) {

            ////10/12
            $this->db->query("UPDATE {$this->table_name} SET notification_has_sent = 0, total_balance = total_balance + {$amount} WHERE id = {$this->id}");
        } else {
            $this->db->query("UPDATE {$this->table_name} SET total_balance = total_balance + {$amount} WHERE id = {$this->id}");
        }

        $this->load->model('balancelog');
        $id = $this->balancelog->insert_by_array(array('user_id' => $this->id, 'points_cnt' => $amount, 'reason' => $reason, 'balance_expire_date' => $balance_expire_date, 'created_by' => $created_by));

        if ($this->db->affected_rows() > 0) {
            return $id;
        } else {
            return false;
        }
    }

    public function change_lang($lang)
    {
        $this->lang = $lang;
        $this->update_by_array(array('lang' => $lang), array('id' => $this->id));
    }

    public function update_last_login_date_ip($offset_time, $ip)
    {
        $login_time = date("Y-m-d H:i");
        $login_time = date("Y-m-d H:i", strtotime("{$login_time} -{$offset_time} minutes"));
        $this->update_by_array(array("last_login_date" => $login_time, "last_login_ip" => $ip), array("id" => $this->id));
    }

    public function update_last_login_date_ip_agent($offset_time, $ip, $agent)
    {
        //get_client_ip
        $login_time = date("Y-m-d H:i");
        $login_time = date("Y-m-d H:i", strtotime("{$login_time} -{$offset_time} minutes"));
        $this->update_by_array(array("last_login_date" => $login_time, "last_login_ip" => $ip, 'last_agent' => $agent), array("id" => $this->id));
    }

    public function update_faild_count_login($offset_time)
    {
        $login_time = date("Y-m-d H:i");
        $login_time = date("Y-m-d H:i", strtotime("{$login_time} -{$offset_time} minutes"));
        $this->update_by_array(array("suspended_at" => $login_time, "faild_count_login" => 0), array("id" => $this->id));
    }

    public function reset_password()
    {
        $new_password = rand(1000000, 9999999);
        $this->update_by_array(array('password' => md5($new_password)), array('id' => $this->id));
        return $new_password;
    }

    public function get_warning_balance()
    {
        $res = $this->db->query("SELECT * FROM {$this->table_name} WHERE active = 1 and blocked=0 and total_balance<notification_limit and notification_number is not null and notification_admin_has_sent = 0 and notification_number <> '' and notification_limit is not null  order by id asc ")->result();
        $this->db->query(
            "UPDATE {$this->table_name} " .
            "SET notification_admin_has_sent = 1 " .
            "where active = 1 and blocked=0 and total_balance<notification_limit and notification_number is not null and notification_admin_has_sent = 0 and notification_number <> '' and notification_limit is not null"
        );

        return $res;
    }

    public function login_otp($otp)
    {
        $this->update_by_array(array('otp' => $otp), array('id' => $this->id));
    }

    public function get_susspend()
    {
        $res = array();
        $users = $this->db->get_where($this->table_name, "suspended_at is not null")->result();
        foreach ($users as $user) {
            $res[] = $this->get_by_object($user);
        }
        return $res;
    }
    // Delete

    public function delete()
    {

        $this->load->model('balancelog');
        $this->balancelog->delete_by_user_id($this->id);

        $this->load->model('balancetransfer');
        $this->balancetransfer->delete_by_user_id($this->id);

        $this->load->model('chargerequestbank');
        $this->chargerequestbank->delete_by_user_id($this->id);

        $this->load->model('chargerequestcheckout');
        $this->chargerequestcheckout->delete_by_user_id($this->id);

        $this->load->model('chargerequestpaypal');
        $this->chargerequestpaypal->delete_by_user_id($this->id);

        $this->load->model('contact');
        $this->contact->delete_by_user_id($this->id);

        $this->load->model('contactgroup');
        $this->contactgroup->delete_by_user_id($this->id);

        $this->load->model('creditdebit');
        $this->creditdebit->delete_by_user_id($this->id);

        $this->load->model('favorite');
        $this->favorite->delete_by_user_id($this->id);

        $this->load->model('gateway');
        $this->gateway->delete_by_user_id($this->id);

        $this->load->model('message');
        $this->message->delete_by_user_id($this->id);

        $this->load->model('sender');
        $this->sender->delete_by_user_id($this->id);

        $this->load->model('ticket');
        $this->ticket->delete_by_user_id($this->id);

        $this->load->model('notification');
        $this->notification->delete_by_user_id($this->id);

        $this->load->model('PasswordHistory');
        $this->PasswordHistory->delete_by_user_id($this->id);

        $subaccounts = $this->get_subaccounts();
        foreach ($subaccounts as $subaccount) {
            $subaccount->delete();
        }

        return parent::delete();
    }

    public function delete_by_ids($ids)
    {

        $this->load->model('balancelog');
        $this->balancelog->delete_by_user_ids($ids);

        $this->load->model('balancetransfer');
        $this->balancetransfer->delete_by_user_ids($ids);

        $this->load->model('chargerequestbank');
        $this->chargerequestbank->delete_by_user_ids($ids);

        $this->load->model('chargerequestcheckout');
        $this->chargerequestcheckout->delete_by_user_ids($ids);

        $this->load->model('chargerequestpaypal');
        $this->chargerequestpaypal->delete_by_user_ids($ids);

        $this->load->model('contact');
        $this->contact->delete_by_user_ids($ids);

        $this->load->model('contactgroup');
        $this->contactgroup->delete_by_user_ids($ids);

        $this->load->model('creditdebit');
        $this->creditdebit->delete_by_user_ids($ids);

        $this->load->model('favorite');
        $this->favorite->delete_by_user_ids($ids);

        $this->load->model('gateway');
        $this->gateway->delete_by_user_ids($ids);

        $this->load->model('message');
        $this->message->delete_by_user_ids($ids);

        $this->load->model('sender');
        $this->sender->delete_by_user_ids($ids);

        $this->load->model('ticket');
        $this->ticket->delete_by_user_ids($ids);

        $this->load->model('notification');
        $this->notification->delete_by_user_ids($ids);

        $subaccounts = $this->get_subaccounts_by_ids($ids);
        foreach ($subaccounts as $subaccount) {
            $subaccount->delete();
        }

        return parent::delete_by_ids($ids);
    }

    public function load_data_subaccountsexcel($parent_id)
    {
        return $this->db->query("select id, username ,name , number , total_balance, spent_balance  FROM {$this->table_name}
            where parent_id='" . $parent_id . "'
            order by id desc
            ")->result_array();
    }

    public function getClient($user_id){
        $query = $this->db->get_where('oauth_clients', array("user_id" => $user_id));
        if ($query->num_rows() > 0) {
            return $this->get_by_object($query->first_row());
        } else {
            return null;
        }

    }

    public function createClient($user_id){
       
        

       
    }
}
