<?php

defined('BASEPATH') or exit('No direct script access allowed');
require_once APPPATH . 'traits/AuthMiddlewareV2.php';
class API extends MY_Controller
{
    use AuthMiddlewareV2;
    protected $exempt_methods = [
        'convertFromUnicode',
        'check_advertising_words_message',
        'check_url_in_message',
        'check_url',
        'Register',
        'check_data_user',
        'update_user',
        'block_unblock',
        'user_delete',
        'update_key',
        'new_sender',
        'delete_sender',
        'charge',
        'balance_add_retrieve',
        'waitingsms',
        'delete_waiting_sms',
        'reset_psw',
        'chk_user',
        'check_user_school',
        'change_pass',
        'change_email',
        'usersenderschool',
        'bnk_chrg',
        'sendsms_old',
        'schoolsendsms',
        '_api_send_sms',
        '_api_send_sms_multi',
        'sendsms_get_id',
        'check_sms_status',
        'groupcontact',
        'newgroup',
        'newcontact',
        'deletegrp',
        'usergroup',
        'usercontact',
        'enter_length',
        'sentsms',
        'getHeaders',
        'dlr',
        'webhook',
        'new_test',
        'new_test_bulk',
        'sendlinksms',
        'shorturl',
        'meetingUrl',
        'check_server'

    ];
    protected $user_details;
    public function __construct()
    {

        parent::__construct();
        $this->load->helper('ratelimit');
        $this->load->model('whitelisturl');
        $this->load->model('apilog');
        // $this->load->model('OAuth');
        $this->load->model('user');
        // $this->load->library('OAuth2');
        limitRequests(get_client_ip());
        $current_method = $this->router->fetch_method();
        if (!in_array($current_method, $this->exempt_methods)) {
            $this->user_details = $this->handle_auth();
        }


    }


    private function convertFromUnicode($message)
    {
        $chrArray[0] = "،";
        $unicodeArray[0] = "060C";
        $chrArray[1] = "؛";
        $unicodeArray[1] = "061B";
        $chrArray[2] = "؟";
        $unicodeArray[2] = "061F";
        $chrArray[3] = "ء";
        $unicodeArray[3] = "0621";
        $chrArray[4] = "آ";
        $unicodeArray[4] = "0622";
        $chrArray[5] = "أ";
        $unicodeArray[5] = "0623";
        $chrArray[6] = "ؤ";
        $unicodeArray[6] = "0624";
        $chrArray[7] = "إ";
        $unicodeArray[7] = "0625";
        $chrArray[8] = "ئ";
        $unicodeArray[8] = "0626";
        $chrArray[9] = "ا";
        $unicodeArray[9] = "0627";
        $chrArray[10] = "ب";
        $unicodeArray[10] = "0628";
        $chrArray[11] = "ة";
        $unicodeArray[11] = "0629";
        $chrArray[12] = "ت";
        $unicodeArray[12] = "062A";
        $chrArray[13] = "ث";
        $unicodeArray[13] = "062B";
        $chrArray[14] = "ج";
        $unicodeArray[14] = "062C";
        $chrArray[15] = "ح";
        $unicodeArray[15] = "062D";
        $chrArray[16] = "خ";
        $unicodeArray[16] = "062E";
        $chrArray[17] = "د";
        $unicodeArray[17] = "062F";
        $chrArray[18] = "ذ";
        $unicodeArray[18] = "0630";
        $chrArray[19] = "ر";
        $unicodeArray[19] = "0631";
        $chrArray[20] = "ز";
        $unicodeArray[20] = "0632";
        $chrArray[21] = "س";
        $unicodeArray[21] = "0633";
        $chrArray[22] = "ش";
        $unicodeArray[22] = "0634";
        $chrArray[23] = "ص";
        $unicodeArray[23] = "0635";
        $chrArray[24] = "ض";
        $unicodeArray[24] = "0636";
        $chrArray[25] = "ط";
        $unicodeArray[25] = "0637";
        $chrArray[26] = "ظ";
        $unicodeArray[26] = "0638";
        $chrArray[27] = "ع";
        $unicodeArray[27] = "0639";
        $chrArray[28] = "غ";
        $unicodeArray[28] = "063A";
        $chrArray[29] = "ف";
        $unicodeArray[29] = "0641";
        $chrArray[30] = "ق";
        $unicodeArray[30] = "0642";
        $chrArray[31] = "ك";
        $unicodeArray[31] = "0643";
        $chrArray[32] = "ل";
        $unicodeArray[32] = "0644";
        $chrArray[33] = "م";
        $unicodeArray[33] = "0645";
        $chrArray[34] = "ن";
        $unicodeArray[34] = "0646";
        $chrArray[35] = "ه";
        $unicodeArray[35] = "0647";
        $chrArray[36] = "و";
        $unicodeArray[36] = "0648";
        $chrArray[37] = "ى";
        $unicodeArray[37] = "0649";
        $chrArray[38] = "ي";
        $unicodeArray[38] = "064A";
        $chrArray[39] = "ـ";
        $unicodeArray[39] = "0640";
        $chrArray[40] = "ً";
        $unicodeArray[40] = "064B";
        $chrArray[41] = "ٌ";
        $unicodeArray[41] = "064C";
        $chrArray[42] = "ٍ";
        $unicodeArray[42] = "064D";
        $chrArray[43] = "َ";
        $unicodeArray[43] = "064E";
        $chrArray[44] = "ُ";
        $unicodeArray[44] = "064F";
        $chrArray[45] = "ِ";
        $unicodeArray[45] = "0650";
        $chrArray[46] = "ّ";
        $unicodeArray[46] = "0651";
        $chrArray[47] = "ْ";
        $unicodeArray[47] = "0652";
        $chrArray[48] = "!";
        $unicodeArray[48] = "0021";
        $chrArray[49] = '"';
        $unicodeArray[49] = "0022";
        $chrArray[50] = "#";
        $unicodeArray[50] = "0023";
        $chrArray[51] = "$";
        $unicodeArray[51] = "0024";
        $chrArray[52] = "%";
        $unicodeArray[52] = "0025";
        $chrArray[53] = "&";
        $unicodeArray[53] = "0026";
        $chrArray[54] = "'";
        $unicodeArray[54] = "0027";
        $chrArray[55] = "(";
        $unicodeArray[55] = "0028";
        $chrArray[56] = ")";
        $unicodeArray[56] = "0029";
        $chrArray[57] = "*";
        $unicodeArray[57] = "002A";
        $chrArray[58] = "+";
        $unicodeArray[58] = "002B";
        $chrArray[59] = ",";
        $unicodeArray[59] = "002C";
        $chrArray[60] = "-";
        $unicodeArray[60] = "002D";
        $chrArray[61] = ".";
        $unicodeArray[61] = "002E";
        $chrArray[62] = "/";
        $unicodeArray[62] = "002F";
        $chrArray[63] = "0";
        $unicodeArray[63] = "0030";
        $chrArray[64] = "1";
        $unicodeArray[64] = "0031";
        $chrArray[65] = "2";
        $unicodeArray[65] = "0032";
        $chrArray[66] = "3";
        $unicodeArray[66] = "0033";
        $chrArray[67] = "4";
        $unicodeArray[67] = "0034";
        $chrArray[68] = "5";
        $unicodeArray[68] = "0035";
        $chrArray[69] = "6";
        $unicodeArray[69] = "0036";
        $chrArray[70] = "7";
        $unicodeArray[70] = "0037";
        $chrArray[71] = "8";
        $unicodeArray[71] = "0038";
        $chrArray[72] = "9";
        $unicodeArray[72] = "0039";
        $chrArray[73] = ":";
        $unicodeArray[73] = "003A";
        $chrArray[74] = ";";
        $unicodeArray[74] = "003B";
        $chrArray[75] = "<";
        $unicodeArray[75] = "003C";
        $chrArray[76] = "=";
        $unicodeArray[76] = "003D";
        $chrArray[77] = ">";
        $unicodeArray[77] = "003E";
        $chrArray[78] = "?";
        $unicodeArray[78] = "003F";
        $chrArray[79] = "@";
        $unicodeArray[79] = "0040";
        $chrArray[80] = "A";
        $unicodeArray[80] = "0041";
        $chrArray[81] = "B";
        $unicodeArray[81] = "0042";
        $chrArray[82] = "C";
        $unicodeArray[82] = "0043";
        $chrArray[83] = "D";
        $unicodeArray[83] = "0044";
        $chrArray[84] = "E";
        $unicodeArray[84] = "0045";
        $chrArray[85] = "F";
        $unicodeArray[85] = "0046";
        $chrArray[86] = "G";
        $unicodeArray[86] = "0047";
        $chrArray[87] = "H";
        $unicodeArray[87] = "0048";
        $chrArray[88] = "I";
        $unicodeArray[88] = "0049";
        $chrArray[89] = "J";
        $unicodeArray[89] = "004A";
        $chrArray[90] = "K";
        $unicodeArray[90] = "004B";
        $chrArray[91] = "L";
        $unicodeArray[91] = "004C";
        $chrArray[92] = "M";
        $unicodeArray[92] = "004D";
        $chrArray[93] = "N";
        $unicodeArray[93] = "004E";
        $chrArray[94] = "O";
        $unicodeArray[94] = "004F";
        $chrArray[95] = "P";
        $unicodeArray[95] = "0050";
        $chrArray[96] = "Q";
        $unicodeArray[96] = "0051";
        $chrArray[97] = "R";
        $unicodeArray[97] = "0052";
        $chrArray[98] = "S";
        $unicodeArray[98] = "0053";
        $chrArray[99] = "T";
        $unicodeArray[99] = "0054";
        $chrArray[100] = "U";
        $unicodeArray[100] = "0055";
        $chrArray[101] = "V";
        $unicodeArray[101] = "0056";
        $chrArray[102] = "W";
        $unicodeArray[102] = "0057";
        $chrArray[103] = "X";
        $unicodeArray[103] = "0058";
        $chrArray[104] = "Y";
        $unicodeArray[104] = "0059";
        $chrArray[105] = "Z";
        $unicodeArray[105] = "005A";
        $chrArray[106] = "[";
        $unicodeArray[106] = "005B";
        $char = "\ ";
        $chrArray[107] = trim($char);
        $unicodeArray[107] = "005C";
        $chrArray[108] = "]";
        $unicodeArray[108] = "005D";
        $chrArray[109] = "^";
        $unicodeArray[109] = "005E";
        $chrArray[110] = "_";
        $unicodeArray[110] = "005F";
        $chrArray[111] = "`";
        $unicodeArray[111] = "0060";
        $chrArray[112] = "a";
        $unicodeArray[112] = "0061";
        $chrArray[113] = "b";
        $unicodeArray[113] = "0062";
        $chrArray[114] = "c";
        $unicodeArray[114] = "0063";
        $chrArray[115] = "d";
        $unicodeArray[115] = "0064";
        $chrArray[116] = "e";
        $unicodeArray[116] = "0065";
        $chrArray[117] = "f";
        $unicodeArray[117] = "0066";
        $chrArray[118] = "g";
        $unicodeArray[118] = "0067";
        $chrArray[119] = "h";
        $unicodeArray[119] = "0068";
        $chrArray[120] = "i";
        $unicodeArray[120] = "0069";
        $chrArray[121] = "j";
        $unicodeArray[121] = "006A";
        $chrArray[122] = "k";
        $unicodeArray[122] = "006B";
        $chrArray[123] = "l";
        $unicodeArray[123] = "006C";
        $chrArray[124] = "m";
        $unicodeArray[124] = "006D";
        $chrArray[125] = "n";
        $unicodeArray[125] = "006E";
        $chrArray[126] = "o";
        $unicodeArray[126] = "006F";
        $chrArray[127] = "p";
        $unicodeArray[127] = "0070";
        $chrArray[128] = "q";
        $unicodeArray[128] = "0071";
        $chrArray[129] = "r";
        $unicodeArray[129] = "0072";
        $chrArray[130] = "s";
        $unicodeArray[130] = "0073";
        $chrArray[131] = "t";
        $unicodeArray[131] = "0074";
        $chrArray[132] = "u";
        $unicodeArray[132] = "0075";
        $chrArray[133] = "v";
        $unicodeArray[133] = "0076";
        $chrArray[134] = "w";
        $unicodeArray[134] = "0077";
        $chrArray[135] = "x";
        $unicodeArray[135] = "0078";
        $chrArray[136] = "y";
        $unicodeArray[136] = "0079";
        $chrArray[137] = "z";
        $unicodeArray[137] = "007A";
        $chrArray[138] = "{";
        $unicodeArray[138] = "007B";
        $chrArray[139] = "|";
        $unicodeArray[139] = "007C";
        $chrArray[140] = "}";
        $unicodeArray[140] = "007D";
        $chrArray[141] = "~";
        $unicodeArray[141] = "007E";
        $chrArray[142] = "©";
        $unicodeArray[142] = "00A9";
        $chrArray[143] = "®";
        $unicodeArray[143] = "00AE";
        $chrArray[144] = "÷";
        $unicodeArray[144] = "00F7";
        $chrArray[145] = "×";
        $unicodeArray[145] = "00F7";
        $chrArray[146] = "§";
        $unicodeArray[146] = "00A7";
        $chrArray[147] = " ";
        $unicodeArray[147] = "0020";
        $chrArray[148] = "\n";
        $unicodeArray[148] = "000D";
        $chrArray[149] = "\r";
        $unicodeArray[149] = "000A";
        $chrArray[150] = "٠";
        $unicodeArray[150] = "0660";
        $chrArray[151] = "١";
        $unicodeArray[151] = "0661";
        $chrArray[152] = "٢";
        $unicodeArray[152] = "0662";
        $chrArray[153] = "٣";
        $unicodeArray[153] = "0663";
        $chrArray[154] = "٤";
        $unicodeArray[154] = "0664";
        $chrArray[155] = "٥";
        $unicodeArray[155] = "0665";
        $chrArray[156] = "٦";
        $unicodeArray[156] = "0666";
        $chrArray[157] = "٧";
        $unicodeArray[157] = "0667";
        $chrArray[158] = "٨";
        $unicodeArray[158] = "0668";
        $chrArray[159] = "٩";
        $unicodeArray[159] = "0669";
        $chrArray[160] = "۰";
        $unicodeArray[160] = "06F0";
        $chrArray[161] = "۱";
        $unicodeArray[161] = "06F1";
        $chrArray[162] = "۲";
        $unicodeArray[162] = "06F2";
        $chrArray[163] = "۳";
        $unicodeArray[163] = "06F3";
        $chrArray[164] = "۴";
        $unicodeArray[164] = "06F4";
        $chrArray[165] = "۵";
        $unicodeArray[165] = "06F5";
        $chrArray[166] = "۶";
        $unicodeArray[166] = "06F6";
        $chrArray[167] = "۷";
        $unicodeArray[167] = "06F7";
        $chrArray[168] = "۸";
        $unicodeArray[168] = "06F8";
        $chrArray[169] = "۹";
        $unicodeArray[169] = "06F8";
        $strResult = "";

        for ($i = 0; $i < strlen($message); $i = $i + 4) {
            if (in_array(substr($message, $i, 4), $unicodeArray))
                $strResult .= $chrArray[array_search(substr($message, $i, 4), $unicodeArray)];
        }

        if (empty($strResult)) {
            $strResult = $message;
        }

        return $strResult;
    }

    private function check_advertising_words_message($message)
    {
        $message_words = explode(" ", $message);
        $advertising_words = explode(",", $this->site_settings['advertising_words']);
        foreach ($message_words as $word) {
            if (in_array($word, $advertising_words)) {
                return true;
            }
        }
        return false;
    }

    private function check_url_in_message($message)
    {
        $message_words = explode(" ", $message);
        foreach ($message_words as $word) {
            if ($this->check_url($word)) {
                $url = isset(parse_url($word)['host']) ? parse_url($word)['host'] : parse_url($word)['path'];
                $whitelist_url = $this->whitelisturl->get_by_url($url);
                if (!empty($whitelist_url)) {
                    continue;
                }
                return true;
            }
        }
        return false;
    }
    private function check_url($url)
    {
        if (filter_var($url, FILTER_VALIDATE_URL) === false) {
            if (filter_var("http://$url", FILTER_VALIDATE_URL) === false) {
                return false;
            }
            $url = "http://$url";
        }

        $host = parse_url($url, PHP_URL_HOST);
        if (!preg_match('/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/', $host)) {
            return false;
        }

        return true;
    }

    // http://localhost/arsl_3/api.php?comm=register&user=api_user_1&pass=123456&name=API%20User%201&mobile=963955222001&email=<EMAIL>
    public function Register()
    {
        // die('-100');
        $this->load->model('user');
        $this->load->library('encryption');
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $name = $this->input->post_get('name');
        $number = $this->input->post_get('mobile');
        $email = $this->input->post_get('email');
        $is_school = $this->input->post_get('is_school');
        $ip = $this->input->post_get('ip');
        $parent_id = $this->input->post_get('parent_id');
        $this->load->model('country');
        $countries = $this->country->get_active();
        for ($i = 1; $i < 5; $i++) {
            foreach ($countries as $country) {
                if ((strlen($country['id']) == $i) && (substr($number, 0, $i) == $country['id'])) {
                    $country_id = $country['id'];
                }
            }
        }
        if (empty($name) || empty($username) || empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL) || empty($password) || empty($number) || empty($country_id)) {
            if ($is_school) {
                header('Content-Type: application/json');
                echo json_encode([
                    'status' => -100,
                    'data' => 'Missing parameters (not exist or empty) Username + password + mobile + email
                '
                ]);
            } else {
                $this->send_error_response(-100,null,null,null,$username);
            }

        } elseif (!$this->user->check_username_availability($username)) {
            if ($is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -110, 'data' => 'Username already used']);
            } else {
                $this->send_error_response(-110, null, 'Username already used', null, $username);
            }
        } elseif (!$this->user->check_email_availability($email)) {
            if ($is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -112, 'data' => 'Email already used']);
            } else {
                $this->send_error_response(-112, null, 'Email already used', null, $username);
            }
        } elseif (!$this->user->check_number_availability($number)) {
            if ($is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -111, 'data' => 'Mobile already used']);
            } else {
                $this->send_error_response(-112, null, 'Mobile already used', null, $username);
            }
        } elseif (!preg_match('/^[a-zA-Z][a-zA-Z0-9_]*$/', $username)) {
            $error_message = "Username must start with a letter and contain only letters, numbers, and underscores.";
            if ($is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -120, 'data' => $error_message]);
            } else {
                $this->send_error_response(-120, null, $error_message, null, $username);
            }
        } elseif (strlen($password) < 6) {
            $error_message = "Password less than 6 chars";
            if ($is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -121, 'data' => $error_message]);
            } else {
                $this->send_error_response(-121, null, $error_message, null, $username);
            }
        } elseif (!$this->verifyPassword($password, $username)) {
            $error_message = "Password does not meet the complexity requirements";
            if ($is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -121, 'data' => $error_message]);
            } else {
                $this->send_error_response(-121, null, $error_message, null, $username);
            }
        } else {
            $activation_code = rand(10000, 99999);
            $specific_date = new DateTime();
            $days = $this->site_settings['password_expiration'];
            $specific_date->modify("+$days days");

            $obj_data = array(
                'id' => NULL,
                'username' => $username,
                'password' => password_hash($password, PASSWORD_DEFAULT),
                'name' => $name,
                'email' => $email,
                'number' => $number,
                'country_id' => $country_id,
                'reg_ip' => $ip ?? get_client_ip(),
                'activation_code' => $activation_code,
                'total_balance' => 0,
                'can_send_ad' => $this->site_settings["send_from_db"],
                'lang' => $this->site_settings['default_lang'],
                'is_school' => $is_school,
                'active' => $is_school,
                'parent_id' => $parent_id,
                'secret_key' => $this->encryption->encrypt(bin2hex(random_bytes(32))),
                'invitation_key' => bin2hex(random_bytes(16)),
                'password_expiration_at' => $specific_date->format('Y-m-d 00:00:00'),
            );
            $user_id = $this->user->insert_by_array($obj_data);
            $user = $this->user->get_by_id($user_id);
            $this->load->model('announcement');
            if (!$is_school) {
                $user->change_balance($this->site_settings['free_balance'], "Registration Givt", date('Y-m-d', strtotime(' + 1 years')), 'system', 0);

                // Welcome Message
                $announcement = $this->announcement->get_by_name('user_welcoming');
                if ($announcement->media == "EMAIL" || $announcement->media == "BOTH") {
                    $this->send_mail(
                        $this->site_settings['site_email'],
                        $this->site_settings['site_name'],
                        $user->email,
                        $announcement->title_ar . " - " . $announcement->title_en,
                        $announcement->text_email,
                        array(
                            '{site_name}' => $this->site_settings['site_name'],
                            '{username}' => $user->username,
                            '{number}' => $user->number,
                            '{activation_code}' => $activation_code
                        )
                    );
                }
                if ($announcement->media == "SMS" || $announcement->media == "BOTH") {
                    if ($announcement->budget == "ADMIN") {
                        $this->send_sms_admin(
                            $this->site_settings['system_sms_sender'],
                            $user->number,
                            $announcement->text_sms,
                            0,
                            array(
                                '{site_name}' => $this->site_settings['site_name'],
                                '{username}' => $user->username,
                                '{number}' => $user->number,
                                '{activation_code}' => $activation_code
                            )
                        );
                    } else {
                        $this->send_sms_user(
                            $this->session->userdata('user_logged_info')['id'],
                            $this->site_settings['system_sms_sender'],
                            $user->number,
                            $announcement->text_sms,
                            0,
                            array(
                                '{site_name}' => $this->site_settings['site_name'],
                                '{username}' => $this->session->userdata('user_logged_info')['username'],
                                '{number}' => $user->number,
                                '{activation_code}' => $activation_code
                            )
                        );
                    }
                }

                $announcement = $this->announcement->get_by_name('mobile_checking');
                if ($announcement->media == "EMAIL" || $announcement->media == "BOTH") {
                    $this->send_mail(
                        $this->site_settings['site_email'],
                        $this->site_settings['site_name'],
                        $user->email,
                        $announcement->title_ar . " - " . $announcement->title_en,
                        $announcement->text_email,
                        array(
                            '{site_name}' => $this->site_settings['site_name'],
                            '{username}' => $user->username,
                            '{number}' => $user->number,
                            '{activation_code}' => $activation_code
                        )
                    );
                }

                if ($announcement->media == "SMS" || $announcement->media == "BOTH") {
                    if ($announcement->budget == "ADMIN") {
                        $this->send_sms_admin(
                            $this->site_settings['system_sms_sender'],
                            $user->number,
                            $announcement->text_sms,
                            0,
                            array(
                                '{site_name}' => $this->site_settings['site_name'],
                                '{username}' => $user->username,
                                '{number}' => $user->number,
                                '{activation_code}' => $activation_code
                            )
                        );
                    } else {
                        $this->send_sms_user(
                            $this->session->userdata('user_logged_info')['id'],
                            $this->site_settings['system_sms_sender'],
                            $user->number,
                            $announcement->text_sms,
                            0,
                            array(
                                '{site_name}' => $this->site_settings['site_name'],
                                '{username}' => $this->session->userdata('user_logged_info')['username'],
                                '{number}' => $user->number,
                                '{activation_code}' => $activation_code
                            )
                        );
                    }
                }
            }


            // Activation Code Message



            $response_data = $user->to_array_mini();
            if ($is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => 999, 'data' => $response_data]);
            } else {
                $this->send_success_response($response_data);
            }
        }
    }

    public function check_data_user()
    {
        $username = $this->input->post_get('username');
        $number = $this->input->post_get('number');
        $email = $this->input->post_get('email');
        $this->load->model('user');
        if (!$this->user->check_username_availability($username)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Username already used']);

        } elseif (!$this->user->check_email_availability($email)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -112, 'data' => 'Email already used']);
        } elseif (!$this->user->check_number_availability($number)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -111, 'data' => 'Mobile already used']);
        } elseif (preg_match('/[\'^�$%&*()}{@#~?><>,|=_+�-] /', $username)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -120, 'data' => 'Username contain special characters or Arabic chars']);
        } else {
            header('Content-Type: application/json');
            echo json_encode(['status' => 1]);
        }

    }

    // http://localhost/arsl_3/api/update_user/?user=api_user_2&secret_key=4b47cdf5fbf91eb6e4bd578aaef486ee&name=APIUser3&email=<EMAIL>&number=966556219141
    public function update_user()
    {

        $super_visor_secret_key = $this->getHeaders()['Secret-Key'];

        $user_id = $this->input->post_get('user_id');
        $username = $this->input->post_get('user');
        $name = $this->input->post_get('name');
        $email = $this->input->post_get('email');
        $password = $this->input->post_get('password');
        $number = $this->input->post_get('number');
        $address = $this->input->post_get('address');
        $secret_key = $this->input->post_get('secret_key');

        $this->load->model('supervisor');

        $supervisor = $this->supervisor->get_by_username('school_supervisor');

        $this->load->model('user');
        $entry = $this->user->get_by_id($user_id);
        // var_dump(password_hash($super_visor_secret_key,PASSWORD_DEFAULT));

        if (empty($supervisor) || empty($super_visor_secret_key) || empty($secret_key) || empty($name) || empty($email) || empty($number)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -100, 'data' => 'Missing parameters (not exist or empty) Username + password + mobile + email']);
        } elseif (empty($supervisor) || !password_verify($super_visor_secret_key, $supervisor->secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Wrong username or password']);
        } elseif (!$this->user->check_email_availability($email, $user_id)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -112, 'data' => 'Email already used']);
        } elseif (!$this->user->check_number_availability($number, $user_id)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -111, 'data' => 'Mobile already used']);
        } elseif (!$this->user->check_username_availability($username, $user_id)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Username already used']);
        } else {

            if (empty($entry) || !$entry->is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -110, 'data' => 'Account not exist']);
            } else {
                $obj_data = array('name' => $name, 'email' => $email, 'number' => $number, 'address' => $address, 'secret_key' => password_hash($secret_key, PASSWORD_DEFAULT));
                $this->user->update_by_array($obj_data, array('id' => $user_id));
                header('Content-Type: application/json');
                echo json_encode(['status' => 999, 'data' => 'Success update user']);
            }
        }
    }

    // http://localhost/arsl_3/api/block_unblock/12/?user=admin&passwored=222
    public function block_unblock()
    {
        $super_visor_secret_key = $this->getHeaders()['Secret-Key'];
        $user_id = $this->input->post_get('user_id');

        $this->load->model('supervisor');
        $supervisor = $this->supervisor->get_by_username('school_supervisor');
        if (empty($user_id) || empty($super_visor_secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -100, 'data' => 'Missing parameters (not exist or empty) Username + secret_key']);
        } elseif (empty($supervisor) || !password_verify($super_visor_secret_key, $supervisor->secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Wrong username or key']);
        } else {
            $this->load->model('user');
            $entry = $this->user->get_by_id($user_id);
            if (empty($entry) || !$entry->is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -110, 'data' => 'Account not exist']);
            } else {
                $entry->block_unblock();
                header('Content-Type: application/json');
                echo json_encode(['status' => 999, 'data' => 'block / unblock user']);

            }
        }
    }

    // http://localhost/arsl_3/api/user_delete/12/?user=admin&passwored=222
    public function user_delete()
    {
        $super_visor_secret_key = $this->getHeaders()['Secret-Key'];
        $user_id = $this->input->post_get('user_id');
        $this->load->model('supervisor');
        $supervisor = $this->supervisor->get_by_username('school_supervisor');
        if (empty($user_id)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -100, 'data' => 'Missing parameters user_id']);
        } elseif (empty($supervisor) || !password_verify($super_visor_secret_key, $supervisor->secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Wrong username or key']);
        } else {
            $this->load->model('user');
            $entry = $this->user->get_by_id($user_id);
            if (empty($entry) || !$entry->is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -110, 'data' => 'Account not exist']);
            } else {
                $entry->delete();
                header('Content-Type: application/json');
                echo json_encode(['status' => 999, 'data' => 'Success deleted user']);
            }
        }
    }

    // http://localhost/arsl_3/api/update_key/12/?user=admin&passwored=222
    public function update_key()
    {
        $super_visor_secret_key = $this->getHeaders()['Secret-Key'];
        $user_id = $this->input->post_get('user_id');
        $this->load->model('supervisor');
        $supervisor = $this->supervisor->get_by_username('school_supervisor');
        if (empty($user_id)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -100, 'data' => 'Missing parameters user_id']);
        } elseif (empty($supervisor) || !password_verify($super_visor_secret_key, $supervisor->secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Wrong username or key']);
        } else {
            $this->load->model('user');
            $entry = $this->user->get_by_id($user_id);
            if (empty($entry) || !$entry->is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -110, 'data' => 'Account not exist']);
            } else {
                $this->load->library('encryption');
                $key = bin2hex(random_bytes(32));
                $obj_data = array('secret_key' => $this->encryption->encrypt($key));
                $this->user->update_by_array($obj_data, array('id' => $entry->id));
                header('Content-Type: application/json');
                echo json_encode(['status' => 999, 'data' => $key]);
            }
        }
    }
    // http://localhost/arsl_3/api.php?comm=activate&user=api_user_1&pass=123456&code=26031    

    public function new_sender()
    {
        $super_visor_secret_key = $this->getHeaders()['Secret-Key'];
        $user_id = $this->input->post_get('user_id');
        $name = $this->input->post_get('sender_text');
        $sender_id = $this->input->post_get('sender_id');

        $this->load->model('supervisor');
        $this->load->model('sender');
        $supervisor = $this->supervisor->get_by_username('school_supervisor');
        if (empty($super_visor_secret_key) || empty($name) || empty($user_id)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -100, 'data' => 'Missing parameters (not exist or empty) Username + secret_key']);
        } elseif (empty($supervisor) || !password_verify($super_visor_secret_key, $supervisor->secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Wrong username or key']);
        } elseif (!$this->sender->check_name_availability($user_id, $name, $sender_id)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -113, 'data' => 'sender  text duplicated ']);
        } else {
            $this->load->model('user');
            $entry = $this->user->get_by_id($user_id);
            if (empty($entry) || !$entry->is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -110, 'data' => 'Account not exist']);
            } else {
                $obj_data = array('name' => $name, 'user_id' => $user_id, 'status' => 0, 'default' => 0);
                if (empty($sender_id)) {
                    $sender = $this->sender->get_by_id($this->sender->insert_by_array($obj_data));
                } else {
                    $obj_data['id'] = $sender_id;
                    $entry = $this->sender->get_by_id($sender_id);
                    $this->sender->update_by_array($obj_data, array('id' => $sender_id));
                    $sender = $this->sender->get_by_id($sender_id);
                }

                log_event_audit([
                    'event_type' => 'RequestSenderName',
                    'event_description' => "User #{$user_id} have requested sender name {$sender->name} via API",
                    'entity_type' => 'Sender',
                    'entity_id' => $sender->id,
                    'changes' => $obj_data,
                    'created_by_id' => $user_id,
                    'created_by_type' => 'User',
                ]);


                header('Content-Type: application/json');
                echo json_encode(['status' => 999, 'data' => $sender->to_array()]);
            }
        }



    }

    public function delete_sender()
    {
        $super_visor_secret_key = $this->getHeaders()['Secret-Key'];
        $user_id = $this->input->post_get('user_id');
        $sender_id = $this->input->post_get('sender_id');

        $this->load->model('supervisor');
        $this->load->model('sender');
        $supervisor = $this->supervisor->get_by_username('school_supervisor');
        if (empty($super_visor_secret_key) || empty($sender_id) || empty($user_id)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -100, 'data' => 'Missing parameters (not exist or empty) Username + secret_key']);
        } elseif (empty($supervisor) || !password_verify($super_visor_secret_key, $supervisor->secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Wrong username or key']);
        } else {
            $this->load->model('user');
            $entry = $this->user->get_by_id($user_id);
            if (empty($entry) || !$entry->is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -110, 'data' => 'Account not exist']);
            } else {
                $entry = $this->sender->get_by_id_user_id($sender_id, $user_id);

                if (!empty($entry)) {
                    $entry->delete();
                    header('Content-Type: application/json');
                    echo json_encode(['status' => 999, 'data' => 'Deleted sender']);
                } else {
                    header('Content-Type: application/json');
                    echo json_encode(['status' => -110, 'data' => 'Sender not exist']);
                }

            }
        }
    }
    // http://localhost/arsl_3/api/charge/12/?user=admin&passwored=222
    public function charge()
    {
        $super_visor_secret_key = $this->getHeaders()['Secret-Key'];
        $user_id = $this->input->post_get('user_id');
        $edition_type = $this->input->post_get('edition_type');
        $points_count = $this->input->post_get('points_count');
        $reason = $this->input->post_get('reason');
        $amount = $this->input->post_get('amount');
        $balance_expire_date = date('Y-m-d', strtotime(' + 1 years'));

        $this->load->model('supervisor');
        $supervisor = $this->supervisor->get_by_username('school_supervisor');
        if (empty($user_id) || empty($super_visor_secret_key) || empty($points_count) || !is_numeric($points_count) || empty($reason)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -100, 'data' => 'Missing parameters (not exist or empty) Username + secret_key']);
        } elseif (empty($supervisor) || !password_verify($super_visor_secret_key, $supervisor->secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Wrong username or key']);
        } else {
            $this->load->model('user');
            $entry = $this->user->get_by_id($user_id);
            if (empty($entry) || !$entry->is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -110, 'data' => 'Account not exist']);
            } else {
                if (strtoupper($edition_type) == 'PLUS') {
                    $entry->change_balance($points_count, $reason, $balance_expire_date, 'DreamsSchool', $amount);
                } else {
                    $entry->change_balance((-1 * $points_count), $reason, null, 'DreamsSchool', 0);
                }

                header('Content-Type: application/json');
                echo json_encode(['status' => 999, 'data' => 'Success']);
            }
        }

    }

    public function balance_add_retrieve()
    {
        $super_visor_secret_key = $this->getHeaders()['Secret-Key'];
        $secret_key = $this->input->post_get('secret_key');
        $this->load->model('supervisor');
        $supervisor = $this->supervisor->get_by_username('school_supervisor');
        $user_id = $this->input->post_get('user_id');
        $sub_account_id = $this->input->post_get('sub_account_id');
        $operation = $this->input->post_get('operation');
        $points_cnt = $this->input->post_get('points_count');
        if (empty($supervisor) || empty($super_visor_secret_key) || empty($secret_key) || empty($sub_account_id) || empty($points_cnt) || $points_cnt < 0 || empty($operation)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -100, 'data' => 'Missing parameters (not exist or empty) ']);
        } elseif (empty($supervisor) || !password_verify($super_visor_secret_key, $supervisor->secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Wrong username or password']);
        } else {
            $this->load->model('user');
            $main_account = $this->user->get_by_id($user_id);
            $sub_account = $this->user->get_by_id($sub_account_id);

            if (empty($main_account) || !$main_account->is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -110, 'data' => 'Account not exist']);
            } else {
                $this->load->model('balancetransfer');
                if (
                    $operation == "ADD" &&
                    $points_cnt <= ($main_account->total_balance - $main_account->spent_balance)
                ) {
                    $main_account->change_balance(-1 * $points_cnt, "Transfer Balance to {$sub_account->username}");
                    if ($main_account->is_hidden == 1) {
                        $sub_account->change_balance($points_cnt, "charge", date('Y-m-d', strtotime(' + 1 years')));
                    } else {
                        $sub_account->change_balance($points_cnt, "Transfer Balance from {$main_account->username}", date('Y-m-d', strtotime(' + 1 years')));
                    }
                    $this->balancetransfer->insert_by_array(array('sender_id' => $main_account->id, 'receiver_id' => $sub_account->id, 'points_cnt' => $points_cnt));
                } elseif (
                    $operation == "RETRIEVE" &&
                    $points_cnt <= ($sub_account->total_balance - $sub_account->spent_balance)
                ) {
                    $main_account->change_balance($points_cnt, "Transfer Balance from {$sub_account->username}", date('Y-m-d', strtotime(' + 1 years')));
                    if ($main_account->is_hidden == 1) {
                        $sub_account->change_balance(-1 * $points_cnt, "RETRIEVE");
                    } else {
                        $sub_account->change_balance(-1 * $points_cnt, "Transfer Balance from {$main_account->username}");
                    }
                    $this->balancetransfer->insert_by_array(array('sender_id' => $sub_account->id, 'receiver_id' => $main_account->id, 'points_cnt' => $points_cnt));
                } else {
                    header('Content-Type: application/json');
                    echo json_encode(['status' => -101, 'data' => 'failed']);
                }

                header('Content-Type: application/json');
                echo json_encode(['status' => 999, 'data' => 'transfer balance']);
            }
        }
    }
    // http://localhost/arsl_3/api/waitingsms/12/?user=admin&passwored=222
    public function waitingsms()
    {
        $super_visor_secret_key = $this->getHeaders()['Secret-Key'];
        $parent_id = $this->input->post_get('parent_id');
        $user_id = $this->input->post_get('user_id');
        $this->load->model('supervisor');
        $supervisor = $this->supervisor->get_by_username('school_supervisor');


        if (empty($super_visor_secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -100, 'data' => 'Missing parameters (not exist or empty) Username + secret_key']);
        } elseif (empty($supervisor) || !password_verify($super_visor_secret_key, $supervisor->secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Wrong username or key']);
        } else {
            $this->load->model('user');
            if (empty($parent_id) || $parent_id == 0) {
                $entry = $this->user->get_by_id($user_id);
            } else {
                $entry = $this->user->get_by_id($parent_id);
            }

            if (empty($entry) || !$entry->is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -110, 'data' => 'Account not exist']);
            } else {
                $this->load->model('message');
                header('Content-Type: application/json');
                echo json_encode(['status' => 999, 'data' => json_decode($this->message->load_data_waiting_user_v2($parent_id, $user_id, ['iSortCol_0' => 0, 'sSortDir_0' => 'desc', 'iDisplayStart' => 0, 'iDisplayLength' => 50]))->aaData]);


            }
        }

    }

    public function delete_waiting_sms()
    {
        $super_visor_secret_key = apache_request_headers()['seckret_key'];
        $parent_id = $this->input->post_get('parent_id');
        $user_id = $this->input->post_get('user_id');
        $this->load->model('supervisor');
        $supervisor = $this->supervisor->get_by_username('school_supervisor');
        $message_id = $this->input->post_get('message_id');

        // $this->load->model('user'); 
        // if($user_type == 'supervisor'){
        //     $this->load->model('supervisor');
        //     $user = $this->supervisor->get_by_username($username);
        // }else{
        //     $user = $this->user->get_by_username($username);
        //     if($user){
        //         $user_id = $user->id;
        //     }

        // }

        if (empty($super_visor_secret_key) || empty($user_id)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -100, 'data' => 'Missing parameters (not exist or empty) Username + secret_key']);
        } elseif (empty($supervisor) || !password_verify($super_visor_secret_key, $supervisor->secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Wrong username or key']);
        } else {
            $this->load->model('user');
            if (empty($parent_id) || $parent_id == 0) {
                $entry = $this->user->get_by_id($user_id);
            } else {
                $entry = $this->user->get_by_id($parent_id);
            }
            if (empty($entry) || !$entry->is_school) {
                header('Content-Type: applicatio    n/json');
                echo json_encode(['status' => -110, 'data' => 'Account not exist']);
            } else {
                $this->load->model('message');
                $entry = $this->message->get_by_id_user_id($message_id, $user_id);
                if (!empty($entry)) {
                    $entry->cancel();
                    header('Content-Type: application/json');
                    echo json_encode(['status' => 999, 'data' => 'Deleted Message']);
                } else {
                    header('Content-Type: application/json');
                    echo json_encode(['status' => -110, 'data' => 'Message not exist']);
                }


            }
        }

    }

    // http://localhost/arsl_3/api.php?comm=activate&user=api_user_1&pass=123456&code=26031    
    public function activate()
    {
        $this->load->model('user');
        $activation_code = $this->input->post_get('code');
        $user = $this->user_details;
        if (empty($activation_code)) {
            $error_code = 'Missing parameters (not exist or empty) Username + secret_key + activation_code';
            if ($user->is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -100, 'data' => $error_code]);
            } else {
                $this->send_error_response(-100, null, $error_code,null,$user->name);
            }
        } elseif (empty($user)) {
            $error_code = 'Wrong username or password';
            if ($user->is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -100, 'data' => 'Wrong username or password']);
            } else {
                $this->send_error_response(-110, null, $error_code,null,$user->name);
            }
        } elseif ($activation_code != $user->activation_code) {
            $error_code = 'Wrong activation_code';
            if ($user->is_school) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -111, 'data' => $error_code]);
            } else {
                $this->send_error_response(-111, null, $error_code,null,$user->name);
            }
        } else {
            $user->update_by_array(array('active' => 1), array('id' => $user->id));
            $respnse_data = [
                'status' => "Success"
            ];
            if ($user->is_school) {
                header('Content-Type: application/json');
                echo json_encode($respnse_data);
            } else {
                $this->send_success_response($respnse_data);
            }

        }
    }

    // http://localhost/arsl_3/api.php?comm=reset_psw&email=<EMAIL>
    public function reset_psw()
    {
        $this->load->model('user');
        $this->load->library('encryption');
        $email = $this->input->post_get('email');
        $user = $this->user->get_by_email($email);
        if (empty($email)) {
            echo "-100";
        } elseif (empty($user)) {
            echo "-110";
        } else {
            $new_password = $user->reset_password();
            $this->load->model('announcement');
            $announcement = $this->announcement->get_by_name('retreiving_password');
            if ($announcement->media == "EMAIL" || $announcement->media == "BOTH") {
                $this->send_mail(
                    $this->site_settings['site_email'],
                    $this->site_settings['site_name'],
                    $user->email,
                    $announcement->title_ar . " - " . $announcement->title_en,
                    $announcement->text_email,
                    array(
                        '{site_name}' => $this->site_settings['site_name'],
                        '{username}' => $user->username,
                        '{password}' => $new_password
                    )
                );
            }
            if ($announcement->media == "SMS" || $announcement->media == "BOTH") {
                if ($announcement->budget == "ADMIN") {
                    $this->send_sms_admin(
                        $this->site_settings['system_sms_sender'],
                        $user->number,
                        $announcement->text_sms,
                        0,
                        array(
                            '{site_name}' => $this->site_settings['site_name'],
                            '{username}' => $user->username,
                            '{password}' => $new_password
                        )
                    );
                } else {
                    $this->send_sms_user(
                        $user->id,
                        $this->site_settings['system_sms_sender'],
                        $user->number,
                        $announcement->text_sms,
                        0,
                        array(
                            '{site_name}' => $this->site_settings['site_name'],
                            '{username}' => $user->username,
                            '{password}' => $new_password
                        )
                    );
                }
            }
            echo "999";
        }
    }

    //  http://localhost/arsl_3/api.php?comm=chk_user&user=api_user_1&pass=123456
    public function chk_user()
    {
        $this->load->model('user');
        $this->load->library('encryption');
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $secret_key = $this->input->post_get('secret_key');
        $user = $this->user->get_by_username($username);
        if (empty($username) || (empty($password) && empty($secret_key))) {
            $this->send_error_response(-100,null,null, null, $username);
        } elseif (empty($user) || (!password_verify($password, $user->password) && $secret_key != $this->encryption->decrypt($user->secret_key))) {
            $this->send_error_response(-110, null, 'invalied user', null, $username);
        } elseif (!$user->active) {
            $this->send_error_response(-111,null, 'not active user', null, $user->name);
        } elseif ($user->blocked) {
            $this->send_error_response(-112,null, 'blocked user', null, $user->name);
        } else {
            $this->send_success_response(['status' => 'Success'], 0, 0, 0, 0, 0, 999);
        }
    }

    public function check_user_school()
    {
        $this->load->model('supervisor');
        $super_visor_secret_key = $this->getHeaders()['Secret-Key'];
        $user_name = $this->input->post_get('user');
        $supervisor = $this->supervisor->get_by_username('school_supervisor');
        if (empty($user_name) || empty($super_visor_secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -100, 'data' => 'Missing parameters (not exist or empty) Username + secret_key']);
        } elseif (empty($supervisor) || !password_verify($super_visor_secret_key, $supervisor->secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Wrong username or key']);
        } else {
            $this->load->model('user');
            $user = $this->user->get_by_username($user_name);
            header('Content-Type: application/json');
            if ($user) {
                echo json_encode(['status' => 999, 'data' => $user->id]);
            } else {
                echo json_encode(['status' => -110, 'data' => "Wrong username"]);
            }

        }


    }

    // http://localhost/arsl_3/api.php?comm=chk_balance&user=api_user_1&pass=123456
    public function chk_balance()
    {
        $user = $this->user_details;
        $response_data = [
            'status' => 'Success',
            'balance' => $user->total_balance - $user->spent_balance
        ];
        $this->send_success_response($response_data, null, 0, 0, 1);
        // echo $user->total_balance - $user->spent_balance;
    }

    // http://localhost/arsl_3/api.php?comm=change_pass&user=api_user_1&pass=123456&newpass=pass_1
    public function change_pass()
    {
        $this->load->model('user');
        $this->load->library('encryption');
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $secret_key = $this->input->post_get('secret_key');
        $new_password = $this->input->post_get('newpass');
        $user = $this->user->get_by_username($username);
        if (empty($username) || (empty($password) && empty($secret_key)) || empty($new_password)) {
            echo "-100";
        } elseif (strlen($new_password) < 6) {
            echo "-121";
        } elseif (empty($user) || (!password_verify($password, $user->password) && $secret_key != $this->encryption->decrypt($user->secret_key))) {
            echo "-110";
        } else {
            $user->update_by_array(array('password' => password_hash($password, PASSWORD_DEFAULT)), array('id' => $user->id));
            echo "999";
        }
    }

    // http://localhost/arsl_3/api.php?comm=change_email&user=api_user_1&pass=123456&newemail=<EMAIL>
    public function change_email()
    {
        $this->load->model('user');
        $this->load->library('encryption');
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $secret_key = $this->input->post_get('secret_key');
        $new_email = $this->input->post_get('newemail');
        $user = $this->user->get_by_username($username);
        if (empty($username) || (empty($password) && empty($secret_key)) || empty($new_email)) {
            echo "-100";
        } elseif (!filter_var($new_email, FILTER_VALIDATE_EMAIL)) {
            echo "-121";
        } elseif (empty($user) || (!password_verify($password, $user->password) && $secret_key != $this->encryption->decrypt($user->secret_key))) {
            echo "-110";
        } elseif (!$user->check_email_availability($new_email, $user->id)) {
            echo "-112";
        } else {
            $user->update_by_array(array('email' => $new_email), array('id' => $user->id));
            echo "999";
        }
    }

    // http://localhost/arsl_3/api.php?comm=newsender&user=api_user_1&pass=123456&sendertext=APItest
    public function newsender()
    {
        $name = $this->input->post_get('sendertext');
        $user = $this->user_details;
        $this->load->model('sender');
        if (empty($name)) {
            $this->send_error_response(-100, null, null, null, $user->name);
        } elseif (empty($user)) {
            $this->send_error_response(-110, null, "invalid user name and secret_key", null, $user->name);
        } elseif (
            !preg_match('/^[0-9A-Za-z,.;:&? _@-]{2,11}+$/', $name) &&
            !(is_numeric($name) && strlen($name) <= 14)
        ) {
            $this->send_error_response(-114, null, "invalid sender Text 'sender text have wrong character or more then 11 char ore number and more 14 number'", null, $user->name);
        } elseif (!$this->sender->check_name_availability($user->id, $name)) {
            $this->send_error_response(-113, null, "sender text duplicated", null, $user->name);
        } else {
            $obj_data = [
                'id' => null,
                'name' => $name,
                'user_id' => $user->id,
                'status' => 0,
                'default' => 0,
                'note' => ''
            ];
            $sender = $this->sender->get_by_id($this->sender->insert_by_array($obj_data));
            if ($user->unlimited_senders) {
                $sender->enable();

                log_event_audit([
                    'event_type' => 'RequestAndAcceptSenderName',
                    'event_description' => "User #{$user->id} have requested sender name {$sender->name} and accepted automatically because user have unlimited_senders via API",
                    'entity_type' => 'Sender',
                    'entity_id' => $sender->id,
                    'changes' => [
                        ['field_name' => 'status', 'old_value' => false, 'new_value' => true],
                    ],
                    'created_by_id' => $user->id,
                    'created_by_type' => 'User',
                ]);

            } else {
                if ($this->site_settings['admin_sender_request'] == "EMAIL" || $this->site_settings['admin_sender_request'] == "BOTH") {
                    $this->send_mail(
                        $this->site_settings['site_email'],
                        $this->site_settings['site_name'],
                        $this->site_settings['receiver_email'],
                        $this->lang->line('lbl_sender_request'),
                        $this->lang->line('msg_info_messages_sender_request'),
                        array('{site_name}' => $this->site_settings['site_name'], '{username}' => $this->session->userdata('user_logged_info')['username'])
                    );
                }
                if ($this->site_settings['admin_sender_request'] == "SMS" || $this->site_settings['admin_sender_request'] == "BOTH") {
                    $this->send_sms_admin(
                        $this->site_settings['system_sms_sender'],
                        $this->site_settings['receiver_number'],
                        strip_tags($this->lang->line('msg_info_messages_sender_request')),
                        0,
                        array('{site_name}' => $this->site_settings['site_name'], '{username}' => $this->session->userdata('user_logged_info')['username'])
                    );
                }
            }
            $response_data = [
                'status' => 'Success'
            ];
            $this->send_success_response($response_data);
        }
    }

    // http://localhost/arsl_3/api.php?comm=senderstatus&user=api_user_1&pass=123456&sendertext=APItest
    public function senderstatus()
    {
        $name = $this->input->post_get('sendertext');
        $user = $this->user_details;
        $this->load->model('sender');
        $sender = $this->sender->get_by_user_id_name(empty($user) ? -1 : $user->id, $name);
        if (empty($name)) {
            $this->send_error_response(-100,null, null, null, $user->name);
        } elseif (empty($user)) {
            $this->send_error_response(-110, null, "Invalid user name and secret_key", null, $user->name);
        } elseif (empty($sender)) {
            $this->send_error_response(-116, null, "Sender text not found in user sender list", null, $user->name);

        } elseif ($sender->status == 0) {
            $this->send_error_response(-117, null, "Sender text inactive", null, $user->name);

        } elseif ($sender->status == 1) {
            $this->send_success_response(['status' => 'Success'], 0, 0, 0, 0, 0, 999);
        } elseif ($sender->status == 2) {
            $this->send_error_response(-118, null, "sender text rejected", null, $user->name);

        } else {
            $this->send_error_response(-117, null, "Sender text inactive", null, $user->name);

        }
    }

    // http://localhost/arsl_3/api.php?comm=usersender&user=api_user_1&pass=123456
    public function usersender()
    {
        $user = $this->user_details;
        if (!empty($user)) {
            $senders = $user->get_senders();
        }
        if (empty($senders)) {
            $this->send_error_response(-117, null, "No Snder for This User", null, $user->name);
        } else {
            $res = [];
            foreach ($senders as $sender) {
                array_push($res, [
                    'name' => $sender['name'],
                    'status' => sender_status($sender['status']),
                    'default' => ($sender['default'] == 0 ? "false" : "true"),
                    'date' => $sender['date'],
                    'note' => $sender['note'] ?? "",
                ]);
            }
            $response_data = array(
                'status' => 'Success',
                'usersender' => $res
            );
            $this->send_success_response($response_data, null, 0, 0, 0, 1);
            // return response_json($res);
        }
    }

    public function usersenderschool()
    {
        $this->load->model('user');
        $this->load->library('encryption');
        $super_visor_secret_key = $this->getHeaders()['Secret-Key'];
        $user_id = $this->input->post_get('user_id');
        $this->load->model('supervisor');
        $supervisor = $this->supervisor->get_by_username('school_supervisor');

        $user = $this->user->get_by_id($user_id);
        if (!empty($user)) {
            $senders = $user->get_senders();
        }
        if (empty($user_id) || (empty($password) && empty($super_visor_secret_key))) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -100, 'data' => 'Missing parameters']);
        } elseif (empty($supervisor) || !password_verify($super_visor_secret_key, $supervisor->secret_key)) {
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Wrong username or password']);
        } elseif (empty($senders)) {
            echo json_encode(['status' => -117, 'data' => 'No Snder for This User']);
        } elseif (!$user->is_school) {
            echo json_encode(['status' => -110, 'data' => 'Wrong username or password']);
        } else {

            // $res = "<usersender>";
            $res = [];
            foreach ($senders as $sender) {
                array_push($res, (object) [
                    'name' => $sender['name'],
                    'status' => sender_status($sender['status']),
                    'default' => ($sender['default'] == 0 ? "false" : "true"),
                    'expire_date' => $sender['expire_date'],
                    'contract_expiration_date' => $sender['contract_expiration_date'],
                    'note' => $sender['note']
                ]);
            }
            return response_json($res);
        }
    }

    //http://localhost/arsl_3/api.php?comm=bnk_chrg&user=nobalaa&pass=123456&points=200&depositamount=15&currency=USD&accountnumber=test&accountname=test&depositdate=2017-07-01 13:15&bankname=alrajhe
    public function bnk_chrg()
    {
        $this->load->model('user');
        $this->load->model('payplan');
        $this->load->library('encryption');
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $secret_key = $this->input->post_get('secret_key');
        $user = $this->user->get_by_username($username);
        $points = $this->input->post_get('points');
        $amount = $this->input->post_get('depositamount');
        $currency = $this->input->post_get('currency');
        $check_payplan = false;
        $payplan = $this->payplan->get_by_price_currency_method($amount, $currency, 'BANK');
        if (!empty($payplan) && $payplan->points_cnt == $points) {
            $check_payplan = true;
        }
        $account_number = $this->input->post_get('accountnumber');
        $bank_name = $this->input->post_get('bankname');
        $account_owner_name = $this->input->post_get('accountname');
        $deposit_date = $this->input->post_get('depositdate');
        if (
            empty($username) || (empty($password) && empty($secret_key)) || empty($points) || empty($amount) ||
            empty($currency) || empty($account_number) || empty($bank_name) ||
            empty($account_owner_name) || empty($deposit_date)
        ) {
            echo "-100";
        } elseif (empty($user) || (!password_verify($password, $user->password) && $secret_key != $this->encryption->decrypt($user->secret_key))) {
            echo "-110";
        } elseif (!$check_payplan) {
            echo "-121";
        } else {
            $obj_data = array(
                'id' => null,
                'user_id' => $user->id,
                'points_cnt' => $payplan->points_cnt,
                'amount' => $payplan->price,
                'currency' => $payplan->currency,
                'bank_name' => $bank_name,
                'account_number' => $account_number,
                'account_owner_name' => $account_owner_name,
                'deposit_date' => $deposit_date,
                'request_date' => date_format(new DateTime(), 'Y-m-d h:i')
            );
            $this->load->model('chargerequestbank');
            $id = $this->chargerequestbank->insert_by_array($obj_data);
            if ($this->site_settings['admin_charging_request'] == "EMAIL" || $this->site_settings['admin_charging_request'] == "BOTH") {
                $this->send_mail(
                    $this->site_settings['site_email'],
                    $this->site_settings['site_name'],
                    $this->site_settings['receiver_email'],
                    $this->lang->line('lbl_charging_request'),
                    $this->lang->line('msg_info_messages_charging_request'),
                    array('{site_name}' => $this->site_settings['site_name'], '{username}' => $user->username)
                );
            }
            if ($this->site_settings['admin_charging_request'] == "SMS" || $this->site_settings['admin_charging_request'] == "BOTH") {
                $this->send_sms_admin(
                    $this->site_settings['system_sms_sender'],
                    $this->site_settings['receiver_number'],
                    strip_tags($this->lang->line('msg_info_messages_charging_request')),
                    0,
                    array('{site_name}' => $this->site_settings['site_name'], '{username}' => $user->username)
                );
            }
            echo "999";
        }
    }

    // http://localhost/arsl_3/api.php?comm=sendsms&user=nobalaa&pass=123456&to=966500000000&message=lasttest&sender=sendername&date=2017-07-31&time=10:30
    public function sendsms_old()
    {

        $this->load->model('user');
        $this->load->model('sender');
        $this->load->model('messagedetails');
        $this->load->model('outbox');
        $this->load->library('curl');
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $to = $this->input->post_get('to');
        $message = $this->input->post_get('message');
        $sender_name = $this->input->post_get('sender');
        $sender = null;
        //Check prohibited words
        $bad_words = $this->site_settings['bad_words'];
        if (!empty($bad_words)) {
            $bad_words = array_filter(explode(",", $bad_words));
            foreach ($bad_words as $word) {
                if (strpos($message, $word) !== false) {
                    die('-220');
                }
            }
        }
        // $p="a83073ebc271c07184678e8269c44c94";
        $user = $this->user->get_by_username($username);
        if (!empty($user) && !empty($sender_name) && password_verify($password, $user->password)) {
            //User id assign
            $user_id = $user->id;
            //Reseller account user id as parent id
            if (!empty($user->parent_id)) {
                $user_id = $user->parent_id;
            }

            $sender = $this->sender->get_by_user_id_name($user_id, $sender_name);

            if (!empty($sender) && $sender->status <> "1") {
                $sender = null;
            }

            //Reseller sender name issue fixed
            if (!empty($user->parent_id) && !in_array($sender->id, explode(",", $user->granted_sender_ids))) {
                $sender = null;
            }
        }

        $date = $this->input->post_get('date');
        $time = $this->input->post_get('time');
        if (empty($username) || empty($password) || empty($to) || empty($message) || empty($sender_name)) {
            echo "-100";
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo "-110";
        } elseif (!$user->active) {
            echo "-111";
        } elseif ($user->blocked) {
            echo "-112";
        } elseif (!$this->site_settings['site_sending_case']) {
            echo "-114";
        } elseif (!$user->unlimited_senders && empty($sender)) {
            echo "-115";
        } elseif (
            !preg_match('/^[0-9A-Za-z,.;:&? _@-]{2,11}+$/', $sender_name) &&
            !(is_numeric($sender_name) && strlen($sender_name) <= 14)
        ) {
            echo "-116";
        } else {
            $unique_suffix = rand(1000000, 9999999);
            file_put_contents('test.log', "login " . $message . $unique_suffix . "\n", FILE_APPEND);
            $this->messagedetails->create_statistics_temp_table($unique_suffix, $message, $to);

            $entries = $this->messagedetails->get_statistics($unique_suffix);
            $total_cnt = 0;
            $total_cost = 0;

            if (!empty($entries)) {
                for ($i = 0; $i < count($entries); $i++) {
                    if ($entries[$i]["coverage_status"] == 1) {
                        $total_cnt += $entries[$i]["cnt"];
                        $total_cost += $entries[$i]["cost"];
                    }
                }
            }

            if ($total_cost == 0) {

                echo "-122";
            } elseif ($total_cost > ($user->total_balance - $user->spent_balance + $user->credit_limit)) {
                echo "-113";
            } else {
                $auth_code = rand(1000000, 9999999);
                if (empty($date)) {
                    $sending_time = null;
                } else {
                    if (empty($time)) {
                        $sending_time = $date;
                    } else {
                        $sending_time = $date . ' ' . $time;
                    }

                    //Valide date time check
                    if (DateTime::createFromFormat('Y-m-d G:i', $sending_time) === FALSE) {
                        $sending_time = null;
                    }
                }

                $entry_id = $this->outbox->insert_by_array(
                    array(
                        'id' => null,
                        'channel' => 'API',
                        'user_id' => $user->id,
                        'text' => $message,
                        'creation_datetime' => date("Y-m-d H:i:s"),
                        'sending_datetime' => $sending_time,
                        'repeation_period' => "0",
                        'repeation_times' => 0,
                        'variables_message' => 0,
                        'sender_name' => $sender_name,
                        'excel_file_numbers' => '',
                        'all_numbers' => $to,
                        'number_index' => 0,
                        'auth_code' => $auth_code
                    )
                );

                $send = $this->curl->_simple_call("get", site_url("user/sms/pre_flush_by_auth_code/{$entry_id}/{$auth_code}"), array(), array("TIMEOUT" => 0));
                $response = json_decode($send, TRUE);
                try {
                    if ($response['type'] != 'success') {
                        $v = var_export($send, true);
                        file_put_contents('responsFail.log', print_r($v, TRUE) . "\n", FILE_APPEND);
                    }


                } catch (Exception $e) {
                    file_put_contents('catch.log', 'Message: ' . $e->getMessage() . "\n", FILE_APPEND);
                }

                // 
                //    
                file_put_contents('test.log', "exsit " . $message . $unique_suffix . "\n", FILE_APPEND);
                echo 'Success';
                // $response = json_decode($send, TRUE);
                // if ($response['type'] == 'success') {
                //     echo 'Success';
                // } else {
                //     echo $response['text'];
                // }
            }

            // $this->messagedetails->drop_statistics_temp_table($user->id);
            $this->messagedetails->drop_statistics_temp_table($unique_suffix);
        }
    }

    /*
     * Calculate & send function 
     * This function working if upgrade our new sms send web version
     * @param $_POST/$_GET
     * @return array
     */

    //http://www.demosms.alnobalaa.biz/sms_732/api.php?comm=sendsms_new&user=nobalaa&pass=123456&to=966500000000&message=lasttest&sender=sendername&date=2017-07-31&time=10:30
    public function sendsms()
    {
        $accept_header = $this->input->get_request_header('Accept');

        $user_info = $this->user_details;
        // log_message('error', 'send_outbox_sms');

        $this->load->model('sender');
        $this->load->model('messagedetails');
        $this->load->model('outbox');
        $this->load->model('operator');
        $this->load->library('curl');
        $this->load->service('IpWhitelist');
        $actual_link = (empty($_SERVER['HTTPS']) ? 'http' : 'https') . "://$_SERVER[HTTP_HOST]";

        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            $parameters = $_POST;
        } else {
            $parameters = $_GET;
        }
        unset($parameters['pass'], $parameters['secret_key']);
        $api_log_id = $this->apilog->insert_by_array(
            array(
                'url' => $actual_link,
                'parameters' => "user:" . $this->input->post_get('user') . "|" . json_encode($parameters),
                'ip' => get_client_ip(),
                'method' => $_SERVER['REQUEST_METHOD']
            )
        );

        //Parameter assign
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $secret_key = $this->input->post_get('secret_key');
        $to = $this->input->post_get('to');
        if ($this->input->post_get('unicode') == "U") {
            $message = $this->convertFromUnicode($this->input->post_get('message'));
        } elseif ($this->input->post_get('unicode') == "H") {
            $binary = hex2bin($this->input->post_get('message'));
            $message = mb_convert_encoding($binary, 'UTF-8', 'UTF-8');
        } else {
            $message = $this->input->post_get('message');
        }

        $sender_name = $this->input->post_get('sender');
        $date = $this->input->post_get('date');
        $time = $this->input->post_get('time');
        $cala_date = $this->input->post_get('calander_date');
        $cala_time = $this->input->post_get('calander_time');
        $is_dlr = $this->input->post_get('is_dlr');
        $json_response = $this->input->post_get('json_response');
        $is_calender = $this->input->post_get('is_calender');
        $alert_text = $this->input->post_get('reminder_text');
        $alert_minuts = $this->input->post_get('reminder');
        $location_url = $this->input->post_get('location_url');

        $advertising_words = false;


        //Check validation
        if (empty($to) || empty($message) || empty($sender_name)) {
            $this->send_error_response('-100', $api_log_id,null,$json_response, $username);
        }

        //Check prohibited words
        $bad_words = $this->site_settings['bad_words'];

        if (!empty($bad_words)) {
            $bad_words = array_filter(explode(",", $bad_words));
            foreach ($bad_words as $word) {
                if (strpos($message, $word) !== false) {
                    //Check your message. There some prohibited words
                    $advertising_words = true;
                }
            }
        }

        //Get user information
        if (empty($is_dlr)) {
            $is_dlr = 0;
        }
        if (empty($json_response)) {
            $json_response = 0;
        }
        //Check active user
        if (!$user_info->active) {
            //User is not active
            $this->send_error_response('-111', $api_log_id,null,$json_response, $username);
        }

        //Check block user
        if ($user_info->blocked) {
            //User is block
            $this->send_error_response('-112', $api_log_id,null,$json_response, $username);
        }
        if ($user_info->is_active_white_ip == 1) {
            $verify_ip = $this->IpWhitelist->verify_ip($user_info);
       
            if (!$verify_ip) {
                $this->send_error_response('-124', $api_log_id,null,$json_response, $username);
            }
        }


        //Check side sending case
        if (!$this->site_settings['site_sending_case']) {
            //Side sending case not found
            $this->send_error_response('-114', $api_log_id,null,$json_response, $username);
        }

        //Check sender name dependency
        if (!preg_match('/^[0-9A-Za-z,.;:&? _@-]{2,11}+$/', $sender_name) && !(is_numeric($sender_name) && strlen($sender_name) <= 14)) {
            //Sender dependancy found
            $this->send_error_response('-116', $api_log_id,null,$json_response, $username);
        }

        //User id assign
        $user_id = $user_info->id;

        //Reseller account user id as parent id
        if (!empty($user_info->parent_id)) {
            if ($this->user->get_by_id($user_info->parent_id)->is_hidden == 0)
                $user_id = $user_info->parent_id;
        }



        $sender = $this->sender->get_by_user_id_name($user_id, $sender_name);
        if (!empty($sender) && $sender->status != "1") {
            $sender = null;
        }


        //Reseller sender name issue fixed
        // if (!empty($user_info->parent_id) && !in_array($sender->id, explode(",", $user_info->granted_sender_ids))) {
        //     $sender = null;
        // }

        //Check unlimited sender
        if (!$user_info->unlimited_senders && empty($sender)) {
            //Unlimited sender issue found
            $this->send_error_response('-115', $api_log_id,null,$json_response, $username);
        }
        if (strpos($sender_name, '-AD')) {
            $this->send_error_response('-119', $api_log_id,null,$json_response, $username);
        }
        if (strpos($sender_name, '-AD')) {
            $offsettime = $this->site_settings['offset_time'];
            $server_time = date("H:i:s", strtotime("now -$offsettime minutes"));
            if (strtotime($server_time) > strtotime("22:00:00") || strtotime($server_time) < strtotime("09:00:00")) {
                $this->send_error_response('-119', $api_log_id,null,$json_response, $username);
            }
        }
        if (!checkDuplicate($message)) {
            $advertising_words = true;
        }

        if ($this->check_advertising_words_message($message) && !strpos($sender_name, '-AD')) {
            $advertising_words = true;
        }

        if ($this->check_url_in_message($message) && $user_info->allow_url == 0) {
            $advertising_words = true;
        }

        if (!empty($is_calender)) {
            $calander_time = $cala_date;
            if (!empty($cala_time)) {
                $calander_time = $cala_date . ' ' . $cala_time;
            }

            //Valide date time check
            if (DateTime::createFromFormat('Y-m-d G:i', $calander_time) === false) {
                $calander_time = null;
            }

            //Compare with today
            if (!empty($calander_time)) {
                $calander_date = new DateTime($calander_time);
                $to_date = new DateTime();
                if ($calander_date < $to_date) {
                    $this->db->trans_rollback();
                    //Selected date not correct
                    $this->send_error_response('-119', $api_log_id,null,$json_response, $username);

                }
                $coordinates = get_lat_long($location_url);
                $calendar = get_calendar_url($user_id, $sender_name, $message, $calander_time, $alert_minuts, $alert_text, $coordinates['lat'], $coordinates['long'], extractLocationTitle($location_url));
                if ($calendar['status'] <> 1) {
                    $this->db->trans_rollback();
                    $this->send_error_response('-118', $api_log_id,null,$json_response, $username);
                }

                $message .= "\n" . $calendar['url'];
            } else {
                $this->send_error_response('-100', $api_log_id,null,$json_response, $username);
            }
        }

        //Get active country info
        $this->load->model('country');
        $get_countries = $this->country->get_active_countries_by_user_v2($user_id, $user_info->is_international);
        $countries = array_column($get_countries, 'price', 'id');
        $operators = $this->operator->get_active_operators(array_keys($countries));
        $country_min_number_count = array_column($get_countries, 'min_number_count', 'id');
        $country_max_number_count = array_column($get_countries, 'max_number_count', 'id');

        //Get message length
        $this->load->helper('message');
        $message_long = calc_message_length($message, $this->setting->get_value_by_name('enter_length'));
        $all_numbers = array_filter(explode(',', $to));
        $all_numbers = array_unique($all_numbers);
        $number_arr = [];
        $cost = [];
        $country_ids = [];
        //Number foreach
        foreach ($all_numbers as $number) {
            //Preparing number
            if (!is_numeric($number)) {
                die('Number not correct');
            }

            if (substr($number, 0, 2) == "05" && strlen($number) == 10) {
                $number = "966" . substr($number, 1);
            } elseif (substr($number, 0, 1) == "5" && strlen($number) == 9) {
                $number = "966" . $number;
            } elseif (substr($number, 0, 4) == "9660" && strlen($number) == 13) {
                $number = str_replace('9660', '966', $number);
            } elseif (substr($number, 0, 5) == "00966" && strlen($number) == 14) {
                $number = str_replace('00966', '966', $number);
            } elseif (substr($number, 0, 4) == "0966" && strlen($number) == 13) {
                $number = str_replace('0966', '966', $number);
            }

            //Get price by country
            foreach ($countries as $key => $price) {
                preg_match("/^{$key}/", substr($number, 0, 5), $match);
                if (!empty($match)) {
                    if ((strlen($number) >= $country_min_number_count[$key]) && (strlen($number) <= $country_max_number_count[$key])) {
                        //Add country id
                        ///add

                        if ($this->input->post_get('user') == "motaz") {
                            // var_dump(!empty($operators[$key]));
                            // die();
                        }
                        $match_status = false;
                        if (!empty($operators[$key])) {
                            foreach ($operators[$key] as $operator) { //Operator foreach
                                $codes = array_filter(explode(",", $operator['code']));
                                foreach ($codes as $code) { //Code foreach
                                    preg_match("/^{$code}/", substr($number, strlen($key), 3), $match_operator);
                                    if (!empty($match_operator)) {
                                        $match_status = true;
                                        $cost[$number] = ($operator['price'] * $message_long);
                                        break;
                                    }
                                } //End code foreach

                                if ($match_status) {
                                    break;
                                }
                            } //End operator foreach
                        }
                        if (!$match_status) {
                            //Add to cost
                            $cost[$number] = ($price * $message_long);
                        }


                        //
                        $country_ids[$number] = $match[0];
                        // $cost[$number] = ($price * $message_long);
                        //Add number 
                        array_push($number_arr, $number);
                        break;
                    }
                }
            } //End country foreach
        } //End number foreach
        //Check number validation
        if (empty($number_arr)) {
            //Check your number. There some problem
            $this->send_error_response('-117', $api_log_id,null,$json_response, $username);
        }

        //Check user balance
        $total_cost = round(array_sum($cost), 1);
        if ((($user_info->total_balance + $user_info->credit_limit) - $user_info->spent_balance) < $total_cost) {
            //Not enought balance
            $this->send_error_response('-113', $api_log_id,null,$json_response, $username);
        }
        //////////////////1/1/2023 mahmoud atif
        if ($sender && $sender->max_sms_one_day != null) {
            $sms_daily = $this->message->get_count_day_by_sender_name($sender->name);
            if (($total_cost + $sms_daily) > $sender->max_sms_one_day) {
                $this->send_error_response('-123', $api_log_id,null,$json_response, $username);
            }
        }
        //////////////////1/1/2023 end

        //Start transaction
        $this->db->db_debug = false;
        $this->db->trans_begin();

        //Deduct balance 12-10-2023
        $deduct_balance = $user_info->change_balance((-1 * round($total_cost, 1)), "Send SMS Campaign");
        if (!$deduct_balance) {
            $this->db->trans_rollback();
            //Balance deduct failed
            $this->send_error_response('-118', $api_log_id,null,$json_response, $username);
        }

        //Datetime param handle
        $sending_time = null;
        if (!empty($date)) {
            $sending_time = $date;
            if (!empty($time)) {
                $sending_time = $date . ' ' . $time;
            }

            //Valide date time check
            if (DateTime::createFromFormat('Y-m-d G:i', $sending_time) === false) {
                $sending_time = null;
            }

            //Compare with today
            if (!empty($sending_time)) {
                $send_date = new DateTime($sending_time);
                $to_date = new DateTime();
                if ($send_date < $to_date) {
                    $this->db->trans_rollback();
                    //Selected date not correct
                    $this->send_error_response('-119', $api_log_id,null,$json_response, $username);
                }

            }
        }

        //Save to outbox   
        $this->load->model('outbox');
        $auth_code = rand(1000000, 9999999);
        $outbox_param = [
            'id' => null,
            'channel' => 'API',
            'user_id' => $user_info->id,
            'text' => $message,
            'count' => count($number_arr),
            'cost' => $total_cost,
            'creation_datetime' => date("Y-m-d H:i:s"),
            'sending_datetime' => $sending_time,
            'repeation_period' => "0",
            'repeation_times' => 0,
            'variables_message' => 0,
            'sender_name' => $sender_name,
            'all_numbers' => $to,
            'auth_code' => $auth_code,
            'advertising' => $advertising_words
        ];

        $outbox_id = $this->outbox->insert_by_array($outbox_param);
        if (empty($outbox_id)) {
            $this->db->trans_rollback();
            //Insert failed to outbox
            $this->send_error_response('-118', $api_log_id,null,$json_response, $username);
        }

        //Sms processing
        $sms_process = $this->_api_send_sms($outbox_id, $auth_code, 0, $number_arr, $country_ids, $cost, $is_dlr);
        if (!$sms_process['status']) {
            $this->send_error_response('-118', $api_log_id,null,$json_response, $username);
        }
        //Complete transaction
        $this->db->trans_commit();

        if ($advertising_words) {
            $this->send_telegram_message($this->lang->line('msg_info_messages_review_msg'));

        } elseif ($sms_process['type'] == "regular") {
            //Send sms
            // asyncSocketGetRequest(site_url("user/sms/pre_send_by_auth_code/{$sms_process['message_id']}/{$auth_code}"));
            $this->curl->_simple_call("get", site_url("user/sms/pre_send_by_auth_code/{$sms_process['message_id']}/{$auth_code}"), array(), array("TIMEOUT" => 1, 'RETURNTRANSFER' => false));

        }
        if (!strpos($sender_name, '-AD')) {
            if (count($number_arr) >= $this->site_settings['num_message_regular']) {
                $this->send_telegram_message($this->lang->line('msg_info_messages_overload_telegram'));

                //     $this->send_sms_admin(
                //         $this->site_settings['system_sms_sender'],
                //         $this->site_settings['receiver_number'],
                //         strip_tags($this->lang->line('msg_info_messages_overload')),
                //         0,
                //         array('{username}' => $this->session->userdata('user_logged_info')['username'])
                //     );
            }
        }
        $response_data = array(
            'status' => 'Success',
            'credits_deducted' => $total_cost,
            'message_id' => $sms_process['message_id'],
            'message_details' => [
                'parts' => $message_long,
                'recipients' => count($number_arr),
                'sender' => $sender_name,
                'scheduled' => !empty($sending_time) ? $sending_time : null
            ],
            'timestamp' => date('Y-m-d H:i:s')
        );
        $this->send_success_responseV2($response_data, $api_log_id, $user_info->is_school, $is_dlr, 200, ($json_response || $user_info->is_school));
    }

    public function sendsms_multi()
    {
        $this->load->model('sender');
        $this->load->model('messagedetails');
        $this->load->model('outbox');
        $this->load->model('operator');
        $this->load->library('curl');
        $actual_link = (empty($_SERVER['HTTPS']) ? 'http' : 'https') . "://$_SERVER[HTTP_HOST]";
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            $parameters = $_POST;
        } else {
            $parameters = $_GET;
        }
        unset($parameters['pass'], $parameters['secret_key'], $parameters['message'], $parameters['to']);
        $api_log_id = $this->apilog->insert_by_array(
            array(
                'url' => $actual_link,
                'parameters' => json_encode($parameters),
                'ip' => get_client_ip(),
                'method' => $_SERVER['REQUEST_METHOD']
            )
        );
        //Parameter assign
        $to = $this->input->post_get('to');



        $sender_name = $this->input->post_get('sender');
        $date = $this->input->post_get('date');
        $time = $this->input->post_get('time');
        $is_dlr = $this->input->post_get('is_dlr');
        $advertising_words = false;

        if (empty($is_dlr)) {
            $is_dlr = 0;
        }
        //Check validation
        if (empty($to) || empty($sender_name)) {
            //User name,Password,To,Message and Sender name required
            $this->send_error_response(-100, $api_log_id, null, null, $sender_name);
        }



        //Get user information
        $user_info = $this->user_details;
        // if (empty($user_info) || !password_verify($password, $user_info->password)) {
        if (empty($user_info)) {

            //User information not found
            $this->send_error_response(-110, $api_log_id, null, null, $sender_name);
        }

        //Check active user
        if (!$user_info->active) {
            //User is not active
            $this->send_error_response(-111, $api_log_id, null, null, $sender_name);
        }

        //Check block user
        if ($user_info->blocked) {
            //User is block
            $this->send_error_response(-112, $api_log_id, null, null, $sender_name);
        }

        $this->load->model('Whitelistip');
        $client_ip = get_client_ip();
        $valid_ip = $this->Whitelistip->get_by_user_id_ip($user_info->id, $client_ip);
        if (!$valid_ip) {
            $this->send_error_response(-124, $api_log_id);
        }

        //Check side sending case
        if (!$this->site_settings['site_sending_case']) {
            //Side sending case not found
            $this->send_error_response(-114, $api_log_id, null, null, $sender_name);
        }

        //Check sender name dependency
        if (!preg_match('/^[0-9A-Za-z,.;:&? _@-]{2,11}+$/', $sender_name) && !(is_numeric($sender_name) && strlen($sender_name) <= 14)) {
            //Sender dependancy found
            $this->send_error_response(-116, $api_log_id, null, null, $sender_name);
        }

        //User id assign
        $user_id = $user_info->id;

        //Reseller account user id as parent id
        if (!empty($user_info->parent_id)) {
            if ($this->user->get_by_id($user_info->parent_id)->is_hidden == 0)
                $user_id = $user_info->parent_id;
        }



        $sender = $this->sender->get_by_user_id_name($user_id, $sender_name);

        if (!empty($sender) && $sender->status != "1") {
            $sender = null;
        }


        //Reseller sender name issue fixed
        // if (!empty($user_info->parent_id) && !in_array($sender->id, explode(",", $user_info->granted_sender_ids))) {
        //     $sender = null;
        // }

        //Check unlimited sender
        if (!$user_info->unlimited_senders && empty($sender)) {
            //Unlimited sender issue found
            $this->send_error_response(-150, $api_log_id, null, null, $sender_name);

        }

        if (strpos($sender_name, '-AD')) {
            $this->send_error_response(-116, $api_log_id, null, null, $sender_name);
        }

        //Get active country info
        $this->load->model('country');
        $get_countries = $this->country->get_active_countries_by_user_v2($user_id, $user_info->is_international);
        $countries = array_column($get_countries, 'price', 'id');
        $operators = $this->operator->get_active_operators(array_keys($countries));
        $country_min_number_count = array_column($get_countries, 'min_number_count', 'id');
        $country_max_number_count = array_column($get_countries, 'max_number_count', 'id');

        //Get message length
        $this->load->helper('message');
        $all_numbers = json_decode($to, true);

        // $all_numbers = array_keys(json_decode($to, true));

        $number_arr = [];
        $num_variable_message = [];
        $cost = [];
        $country_ids = [];
        $enter_length = $this->setting->get_value_by_name('enter_length');
        //Number foreach
        foreach ($all_numbers as $number) {
            $cost_number = 1;
            //Preparing number
            if (!is_numeric($number['to'])) {
                $this->send_error_response(-117, $api_log_id, null, null, $sender_name);
            }

            if (substr($number['to'], 0, 2) == "05" && strlen($number['to']) == 10) {
                $number = "966" . substr($number, 1);
            } elseif (substr($number['to'], 0, 1) == "5" && strlen($number['to']) == 9) {
                $number['to'] = "966" . $number['to'];
            } elseif (substr($number['to'], 0, 4) == "9660" && strlen($number['to']) == 13) {
                $number['to'] = str_replace('9660', '966', $number['to']);
            } elseif (substr($number['to'], 0, 5) == "00966" && strlen($number['to']) == 14) {
                $number['to'] = str_replace('00966', '966', $number['to']);
            } elseif (substr($number['to'], 0, 4) == "0966" && strlen($number['to']) == 13) {
                $number['to'] = str_replace('0966', '966', $number['to']);
            }

            //Get price by country
            foreach ($countries as $key => $price) {
                preg_match("/^{$key}/", substr($number['to'], 0, 5), $match);
                if (!empty($match)) {
                    if ((strlen($number['to']) >= $country_min_number_count[$key]) && (strlen($number['to']) <= $country_max_number_count[$key])) {
                        $match_status = false;
                        if (!empty($operators[$key])) {
                            foreach ($operators[$key] as $operator) { //Operator foreach
                                $codes = array_filter(explode(",", $operator['code']));
                                foreach ($codes as $code) { //Code foreach
                                    preg_match("/^{$code}/", substr($number['to'], strlen($key), 3), $match_operator);
                                    if (!empty($match_operator)) {
                                        $match_status = true;
                                        $cost_number = ($operator['price'] * calc_message_length($number['message'], $enter_length));
                                        $cost[] = ['to' => $number['to'], 'price' => $cost_number];
                                        break;
                                    }
                                } //End code foreach

                                if ($match_status) {
                                    break;
                                }
                            } //End operator foreach
                        }
                        if (!$match_status) {
                            //Add to cost
                            $cost_number = ($price * calc_message_length($number['message'], $enter_length));
                            $cost[] = ['to' => $number['to'], 'price' => $cost_number];
                        }

                        $country_ids[$number['to']] = $match[0];
                        $num_variable_message[] = ['to' => $number['to'], 'message' => $number['message'], 'length' => calc_message_length($number['to'], $enter_length), 'cost' => $cost_number];
                        break;
                    }
                }
            } //End country foreach
        } //End number foreach
        //Check number validation
        if (empty($num_variable_message)) {
            //Check your number. There some problem
            $this->send_error_response(-117, $api_log_id, null, null, $sender_name);
        }
        //Check user balance
        $total_cost = round(array_sum(array_column($cost, 'price')), 1);
        if ((($user_info->total_balance + $user_info->credit_limit) - $user_info->spent_balance) < $total_cost) {
            //Not enought balance
            $this->send_error_response(-113, $api_log_id, null, null, $sender_name);
        }

        //Start transaction
        $this->db->db_debug = false;
        $this->db->trans_begin();

        //Deduct balance 12-10-2023
        $deduct_balance = $user_info->change_balance((-1 * round($total_cost, 1)), "Send SMS Campaign");
        if (!$deduct_balance) {
            $this->db->trans_rollback();
            //Balance deduct failed
            $this->send_error_response(-118, $api_log_id, null, null, $sender_name);
        }

        //Datetime param handle
        $sending_time = null;
        if (!empty($date)) {
            $sending_time = $date;
            if (!empty($time)) {
                $sending_time = $date . ' ' . $time;
            }

            //Valide date time check
            if (DateTime::createFromFormat('Y-m-d G:i', $sending_time) === false) {
                $sending_time = null;
            }

            //Compare with today
            if (!empty($sending_time)) {
                $send_date = new DateTime($sending_time);
                $to_date = new DateTime();
                if ($send_date < $to_date) {
                    $this->db->trans_rollback();
                    //Selected date not correct
                    $this->send_error_response(-119, $api_log_id, null, null, $sender_name);
                }

            }
        }

        //Save to outbox   
        $this->load->model('outbox');
        $auth_code = rand(1000000, 9999999);
        $outbox_param = [
            'id' => null,
            'channel' => 'API',
            'user_id' => $user_info->id,
            'text' => "*messages*",
            'count' => count($num_variable_message),
            'cost' => $total_cost,
            'creation_datetime' => date("Y-m-d H:i:s"),
            'sending_datetime' => $sending_time,
            'repeation_period' => "0",
            'repeation_times' => 0,
            'variables_message' => 1,
            'sender_name' => $sender_name,
            'all_numbers' => $to,
            'auth_code' => $auth_code,
            'advertising' => $advertising_words
        ];

        $outbox_id = $this->outbox->insert_by_array($outbox_param);
        if (empty($outbox_id)) {
            $this->db->trans_rollback();
            //Insert failed to outbox
            $this->send_error_response(-118, $api_log_id, null, null, $sender_name);
        }
        //Sms processing

        $sms_process = $this->_api_send_sms_multi($outbox_id, $auth_code, 0, array_column($num_variable_message, 'to'), $country_ids, $cost, $is_dlr, $num_variable_message);

        if (!$sms_process['status']) {
            $this->db->trans_rollback();
            $this->send_error_response(-118, $api_log_id, null, null, $sender_name);
        }
        //Complete transaction
        $this->db->trans_commit();

        if ($advertising_words) {
            if ($this->site_settings['advertising_message'] == "SMS" || $this->site_settings['advertising_message'] == "BOTH") {
                $recive_numbers = explode(",", $this->site_settings['receiver_number_advertising']);
                foreach ($recive_numbers as $recive_number) {
                    $this->send_sms_admin(
                        $this->site_settings['system_sms_sender'],
                        $recive_number,
                        strip_tags($this->lang->line('msg_info_messages_review_msg')),
                        0,
                        null
                    );
                }

            }
        } elseif ($sms_process['type'] == "regular") {
            //Send sms

            $this->curl->_simple_call("get", site_url("user/sms/pre_send_by_auth_code/{$sms_process['message_id']}/{$auth_code}"), array(), array("TIMEOUT" => 3));

        }
        if (!strpos($sender_name, '-AD')) {
            if (count($number_arr) >= $this->site_settings['num_message_regular']) {
                $this->send_telegram_message($this->lang->line('msg_info_messages_overload_telegram'));

                // $this->send_sms_admin(
                //     $this->site_settings['system_sms_sender'],
                //     $this->site_settings['receiver_number'],
                //     strip_tags($this->lang->line('msg_info_messages_overload')),
                //     0,
                //     array('{username}' => $this->session->userdata('user_logged_info')['username'])
                // );
            }
        }
        $response_data = array(
            'status' => 'Success',
            'credits_deducted' => $total_cost,
            'message_id' => $sms_process['message_id']
        );
        $this->send_success_response($response_data, $api_log_id, $user_info->is_school, $is_dlr);
    }

    public function schoolsendsms()
    {
        $this->load->model('user');
        $this->load->model('sender');
        $this->load->model('messagedetails');
        $this->load->model('outbox');
        $this->load->model('operator');
        $this->load->library('curl');
        $this->load->model('supervisor');
        $this->load->library('encryption');
        $actual_link = (empty($_SERVER['HTTPS']) ? 'http' : 'https') . "://$_SERVER[HTTP_HOST]";
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            $parameters = $_POST;
        } else {
            $parameters = $_GET;
        }
        unset($parameters['pass'], $parameters['secret_key'], $parameters['message'], $parameters['to']);
        $api_log_id = $this->apilog->insert_by_array(
            array(
                'url' => $actual_link,
                'parameters' => json_encode($parameters),
                'ip' => get_client_ip(),
                'method' => $_SERVER['REQUEST_METHOD']
            )
        );

        //Parameter assign
        $super_visor_secret_key = $this->getHeaders()['Secret-Key'];
        $user_id = $this->input->post_get('user_id');

        $secret_key = $this->input->post_get('secret_key');

        $to = $this->input->post_get('to');
        $message = $this->input->post_get('message');
        $sender_name = $this->input->post_get('sender');
        $date = $this->input->post_get('date');
        $time = $this->input->post_get('time');
        $ip = $this->input->post_get('ip');
        $supervisor = $this->supervisor->get_by_username('school_supervisor');
        //Check validation
        if (empty($user_id) || empty($to) || empty($message) || empty($sender_name) || empty($secret_key)) {//|| empty($supervisor) || empty($super_visor_secret_key)
            //User name,Password,To,Message and Sender name required
            header('Content-Type: application/json');
            echo json_encode(['status' => -100, 'data' => 'Missing parameters']);
            die();
        }

        //Check prohibited words
        $bad_words = $this->site_settings['bad_words'];
        if (!empty($bad_words)) {
            $bad_words = array_filter(explode(",", $bad_words));
            foreach ($bad_words as $word) {
                if (strpos($message, $word) !== false) {
                    //Check your message. There some prohibited words
                    header('Content-Type: application/json');
                    echo json_encode(['status' => -220, 'data' => 'bad words']);
                    die();
                }
            }
        }

        //Get user information
        $user_info = $this->user->get_by_id($user_id);

        if (empty($user_info) || $secret_key != $this->encryption->decrypt($user_info->secret_key)) {//|| !password_verify($super_visor_secret_key, $supervisor->secret_key)
            //User information not found
            header('Content-Type: application/json');
            echo json_encode(['status' => -110, 'data' => 'Wrong username or key']);
            die();
        }

        //Check active user
        if (!$user_info->active) {
            //User is not active
            header('Content-Type: application/json');
            echo json_encode(['status' => -111, 'data' => 'User is not active']);
            die();
        }

        //Check block user
        if ($user_info->blocked) {
            //User is block
            header('Content-Type: application/json');
            echo json_encode(['status' => -112, 'data' => 'User is block']);
            die();
        }

        //Check side sending case
        if (!$this->site_settings['site_sending_case']) {
            //Side sending case not found
            header('Content-Type: application/json');
            echo json_encode(['status' => -114, 'data' => 'Side sending case not found']);
            die();
        }

        //Check sender name dependency
        if (!preg_match('/^[0-9A-Za-z,.;:&? _@-]{2,11}+$/', $sender_name) && !(is_numeric($sender_name) && strlen($sender_name) <= 14)) {
            //Sender dependancy found
            header('Content-Type: application/json');
            echo json_encode(['status' => -116, 'data' => 'Sender dependancy found']);
            die('-116');
        }

        //User id assign
        $user_id = $user_info->id;

        //Reseller account user id as parent id
        if (!empty($user_info->parent_id)) {
            if ($this->user->get_by_id($user_info->parent_id)->is_hidden == 0)
                $user_id = $user_info->parent_id;
        }



        $sender = $this->sender->get_by_user_id_name($user_id, $sender_name);

        if (!empty($sender) && $sender->status != "1") {
            $sender = null;
        }


        //Reseller sender name issue fixed
        // if (!empty($user_info->parent_id) && !in_array($sender->id, explode(",", $user_info->granted_sender_ids))) {
        //     $sender = null;
        // }

        //Check unlimited sender
        if (!$user_info->unlimited_senders && empty($sender)) {
            //Unlimited sender issue found
            header('Content-Type: application/json');
            echo json_encode(['status' => -115, 'data' => 'Unlimited sender issue found']);
            die();
        }

        if (strpos($sender_name, '-AD')) {
            $offsettime = $this->site_settings['offset_time'];
            $server_time = date("H:i:s", strtotime("now -$offsettime minutes"));
            if (strtotime($server_time) > strtotime("22:00:00") || strtotime($server_time) < strtotime("09:00:00")) {
                header('Content-Type: application/json');
                echo json_encode(['status' => -119, 'data' => 'Unlimited sender issue found']);
                die();
            }
        }


        $advertising_words = false;
        if ($this->check_advertising_words_message($message) && !strpos($sender_name, '-AD')) {
            $advertising_words = true;
        }

        if ($this->check_url_in_message($message) && $user_info->allow_url == 0) {
            $advertising_words = true;
        }


        //Get active country info
        $this->load->model('country');
        $get_countries = $this->country->get_active_countries_by_user($user_id);
        $countries = array_column($get_countries, 'price', 'id');
        $operators = $this->operator->get_active_operators(array_keys($countries));
        $country_min_number_count = array_column($get_countries, 'min_number_count', 'id');
        $country_max_number_count = array_column($get_countries, 'max_number_count', 'id');

        //Get message length
        $this->load->helper('message');
        $message_long = calc_message_length($message, $this->setting->get_value_by_name('enter_length'));
        $all_numbers = array_filter(explode(',', $to));
        $all_numbers = array_unique($all_numbers);
        $number_arr = [];
        $cost = [];
        $country_ids = [];
        //Number foreach
        foreach ($all_numbers as $number) {
            //Preparing number
            if (!is_numeric($number)) {
                die('Number not correct');
            }

            if (substr($number, 0, 2) == "05" && strlen($number) == 10) {
                $number = "966" . substr($number, 1);
            } elseif (substr($number, 0, 1) == "5" && strlen($number) == 9) {
                $number = "966" . $number;
            }

            //Get price by country
            foreach ($countries as $key => $price) {
                preg_match("/^{$key}/", substr($number, 0, 5), $match);
                if (!empty($match)) {
                    if ((strlen($number) >= $country_min_number_count[$key]) && (strlen($number) <= $country_max_number_count[$key])) {
                        //Add country id
                        ///add

                        if ($this->input->post_get('user') == "motaz") {
                            // var_dump(!empty($operators[$key]));
                            // die();
                        }
                        $match_status = false;
                        if (!empty($operators[$key])) {
                            foreach ($operators[$key] as $operator) { //Operator foreach
                                $codes = array_filter(explode(",", $operator['code']));
                                foreach ($codes as $code) { //Code foreach
                                    preg_match("/^{$code}/", substr($number, strlen($key), 3), $match_operator);
                                    if (!empty($match_operator)) {
                                        $match_status = true;
                                        $cost[$number] = ($operator['price'] * $message_long);
                                        break;
                                    }
                                } //End code foreach

                                if ($match_status) {
                                    break;
                                }
                            } //End operator foreach
                        }
                        if (!$match_status) {
                            //Add to cost
                            $cost[$number] = ($price * $message_long);
                        }


                        //
                        $country_ids[$number] = $match[0];
                        // $cost[$number] = ($price * $message_long);
                        //Add number 
                        array_push($number_arr, $number);
                        break;
                    }
                }
            } //End country foreach
        } //End number foreach
        //Check number validation
        if (empty($number_arr)) {
            //Check your number. There some problem
            header('Content-Type: application/json');
            echo json_encode(['status' => -117, 'data' => 'Check your number. There some problem']);
            die();
        }

        //Check user balance
        $total_cost = round(array_sum($cost), 1);
        if ((($user_info->total_balance + $user_info->credit_limit) - $user_info->spent_balance) < $total_cost) {
            //Not enought balance
            header('Content-Type: application/json');
            echo json_encode(['status' => -113, 'data' => 'Not enought balance']);
            die();
        }
        //////////////////1/1/2023 mahmoud atif
        /* if($sender){
             $sms_daily = $this->message->get_count_day_by_sender_name($sender->name);
             if($sender->max_sms_one_day != null  && ($total_cost+$sms_daily) > $sender->max_sms_one_day){
                 die("-123");
             }
         }*/
        //////////////////1/1/2023 end

        //Start transaction
        $this->db->db_debug = false;
        $this->db->trans_begin();

        //Deduct balance
        $deduct_balance = $user_info->change_balance((-1 * round($total_cost, 1)), "Send SMS Campaign");
        if (!$deduct_balance) {
            $this->db->trans_rollback();
            //Balance deduct failed
            header('Content-Type: application/json');
            echo json_encode(['status' => -118, 'data' => 'Balance deduct failed']);
            die('-118');
        }

        //Datetime param handle
        $sending_time = null;
        if (!empty($date)) {
            $sending_time = $date;
            if (!empty($time)) {
                $sending_time = $date . ' ' . $time;
            }

            //Valide date time check
            if (DateTime::createFromFormat('Y-m-d G:i', $sending_time) === false) {
                $sending_time = null;
            }

            //Compare with today
            if (!empty($sending_time)) {
                $send_date = new DateTime($sending_time);
                $to_date = new DateTime();
                if ($send_date < $to_date) {
                    $this->db->trans_rollback();
                    //Selected date not correct
                    header('Content-Type: application/json');
                    echo json_encode(['status' => -119, 'data' => 'Selected date not correct']);
                    die();
                }
                if (strpos($sender, '-AD')) {
                    // if(strtotime(date("H:i:s",$sending_time)) > strtotime("20:00:00")  || strtotime(date("H:i:s",$sending_time)) < strtotime("09:00:00")){
                    //     header('Content-Type: application/json');
                    //     echo json_encode( ['status'=>-119,'data'=>'Selected date not correct'] );
                    //     die('-119');
                    // }
                }

            }
        }

        //Save to outbox   
        $this->load->model('outbox');
        $auth_code = rand(1000000, 9999999);
        $outbox_param = [
            'id' => null,
            'channel' => 'API',
            'user_id' => $user_info->id,
            'text' => $message,
            'count' => count($number_arr),
            'cost' => $total_cost,
            'creation_datetime' => date("Y-m-d H:i:s"),
            'sending_datetime' => $sending_time,
            'repeation_period' => "0",
            'repeation_times' => 0,
            'variables_message' => 0,
            'sender_name' => $sender_name,
            'all_numbers' => $to,
            'auth_code' => $auth_code,
            'advertising' => $advertising_words
        ];

        $outbox_id = $this->outbox->insert_by_array($outbox_param);
        if (empty($outbox_id)) {
            $this->db->trans_rollback();
            //Insert failed to outbox
            die('-118');
        }

        //Sms processing
        $sms_process = $this->_api_send_sms($outbox_id, $auth_code, 0, $number_arr, $country_ids, $cost);
        if (!$sms_process['status']) {
            $this->db->trans_rollback();
            die('-118');
        }
        //Complete transaction
        $this->db->trans_commit();

        if ($advertising_words) {
            $this->send_telegram_message($this->lang->line('msg_info_messages_review_msg'));
            // if ($this->site_settings['advertising_message'] == "SMS" || $this->site_settings['advertising_message'] == "BOTH") {
            //     $recive_numbers = explode(",", $this->site_settings['receiver_number_advertising']);
            //     foreach ($recive_numbers as $recive_number) {
            //         $this->send_sms_admin(
            //             $this->site_settings['system_sms_sender'],
            //             $recive_number,
            //             strip_tags($this->lang->line('msg_info_messages_review_msg')),
            //             0,
            //             null
            //         );
            //     }

            // }
        } elseif ($sms_process['type'] == "regular") {
            //Send sms
            $this->curl->_simple_call("get", site_url("user/sms/pre_send_by_auth_code/{$sms_process['message_id']}/{$auth_code}"), array(), array("TIMEOUT" => 3));

        }
        if (!strpos($sender_name, '-AD')) {
            if (count($number_arr) >= $this->site_settings['num_message_regular']) {
                $this->send_telegram_message($this->lang->line('msg_info_messages_overload_telegram'));

                // $this->send_sms_admin(
                //     $this->site_settings['system_sms_sender'],
                //     $this->site_settings['receiver_number'],
                //     strip_tags($this->lang->line('msg_info_messages_overload')),
                //     0,
                //     array('{username}' => $this->session->userdata('user_logged_info')['username'])
                // );
            }
        }

        //orginal response: -999:20031:966521021542
        if ($user_info->is_school == 1) {
            header("Content-type: application/json");
            echo json_encode(['status' => 'Success', 'credits_deducted' => $total_cost]);
        } else {
            die('Success|' . $total_cost);
        }

    }

    /*
     * This function use for api send sms
     * Use transaction befor use the function
     * @return array()
     */

    private function _api_send_sms($outbox_id, $auth_code, $encrypt_number, $number_arr, $country_ids, $cost, $is_dlr = 0, $num_variable_message = false)
    {
        // die();
        //Get outbox info

        $outbox_info = $this->outbox->get_by_id_auth_code($outbox_id, $auth_code);
        if (empty($outbox_info)) {
            $this->db->trans_rollback();
            return ['status' => false, 'code' => 0, 'message' => "Outbox sms not found"];
        }

        //Get user info
        $user_info = $this->user->get_by_id($outbox_info->user_id);

        if (empty($user_info)) {
            $this->db->trans_rollback();
            return ['status' => false, 'code' => 0, 'message' => "User information not found"];
        }

        //Regular message send
        if (empty($outbox_info->sending_datetime)) {
            //Insert to message tb
            $message_param = [
                'user_id' => $outbox_info->user_id,
                'channel' => $outbox_info->channel,
                'text' => $outbox_info->text,
                'creation_datetime' => date("Y-m-d H:i:s"),
                'updation_datetime' => date("Y-m-d H:i:s"),
                'sending_datetime' => $outbox_info->sending_datetime,
                'variables_message' => $outbox_info->variables_message,
                'count' => $outbox_info->count,
                'cost' => $outbox_info->cost,
                'sender_name' => $outbox_info->sender_name,
                'auth_code' => $outbox_info->auth_code,
                'advertising' => $outbox_info->advertising,
                'is_dlr' => $is_dlr,
                'sent_cnt_v2' => get_client_ip()
            ];
            $message_id = $this->message->insert_by_array($message_param);
            if (empty($message_id)) {
                $this->db->trans_rollback();
                return ['status' => false, 'code' => 0, 'message' => "Message insert failed"];
            }
            //Insert to message details
            $batch_insert = $this->messagedetails->insert_batch($message_id, $encrypt_number, $number_arr, $country_ids, $cost, $num_variable_message);
            if (empty($batch_insert)) {
                $this->db->trans_rollback();
                return ['status' => false, 'code' => 0, 'message' => "Message details insert failed"];
            }

            //Delete outbox message
            $outbox_info->delete();
            //Send successfully
            return ['status' => true, 'code' => 0, 'message' => "Message send successfully", 'message_id' => $message_id, 'type' => "regular"];
        }

        //Later message send
        if (!empty($outbox_info->sending_datetime)) {
            //Insert to message tb
            $message_param = [
                'user_id' => $outbox_info->user_id,
                'channel' => $outbox_info->channel,
                'text' => $outbox_info->text,
                'creation_datetime' => date("Y-m-d H:i:s"),
                'sending_datetime' => $outbox_info->sending_datetime,
                'variables_message' => $outbox_info->variables_message,
                'count' => $outbox_info->count,
                'cost' => $outbox_info->cost,
                'sender_name' => $outbox_info->sender_name,
                'auth_code' => $outbox_info->auth_code,
                'advertising' => $outbox_info->advertising,
                'is_dlr' => $is_dlr
            ];

            $message_id = $this->message->insert_by_array($message_param);
            if (empty($message_id)) {
                $this->db->trans_rollback();
                return ['status' => false, 'code' => 0, 'message' => "Message insert failed"];
            }

            //Insert to message details
            $batch_insert = $this->messagedetails->insert_batch($message_id, $encrypt_number, $number_arr, $country_ids, $cost);
            if (empty($batch_insert)) {
                $this->db->trans_rollback();
                return ['status' => false, 'code' => 0, 'message' => "Message details insert failed"];
            }
            //Update message status to message                    
            if (!empty($outbox_info->variables_message) && empty($outbox_info->sending_datetime)) {
                $update_message = $this->message->update_by_array(array('status' => 1), array('id' => $message_id));
                if (empty($update_message)) {
                    $this->db->trans_rollback();
                    return ['status' => false, 'code' => 0, 'message' => "Message status update failed"];
                }
            }

            //Delete outbox message
            $outbox_info->delete();
            //Send successfully
            return ['status' => true, 'code' => 0, 'message' => "Later message send successfully", 'message_id' => $message_id, 'type' => "later"];
        }
    }

    private function _api_send_sms_multi($outbox_id, $auth_code, $encrypt_number, $number_arr, $country_ids, $cost, $is_dlr = 0, $num_variable_message = false)
    {
        // die();
        //Get outbox info

        $outbox_info = $this->outbox->get_by_id_auth_code($outbox_id, $auth_code);
        if (empty($outbox_info)) {
            $this->db->trans_rollback();
            return ['status' => false, 'code' => 0, 'message' => "Outbox sms not found"];
        }

        //Get user info
        $user_info = $this->user->get_by_id($outbox_info->user_id);

        if (empty($user_info)) {
            $this->db->trans_rollback();
            return ['status' => false, 'code' => 0, 'message' => "User information not found"];
        }

        //Regular message send
        if (empty($outbox_info->sending_datetime)) {
            //Insert to message tb
            $message_param = [
                'user_id' => $outbox_info->user_id,
                'channel' => $outbox_info->channel,
                'text' => $outbox_info->text,
                'creation_datetime' => date("Y-m-d H:i:s"),
                'updation_datetime' => date("Y-m-d H:i:s"),
                'sending_datetime' => $outbox_info->sending_datetime,
                'variables_message' => $outbox_info->variables_message,
                'count' => $outbox_info->count,
                'cost' => $outbox_info->cost,
                'sender_name' => $outbox_info->sender_name,
                'auth_code' => $outbox_info->auth_code,
                'advertising' => $outbox_info->advertising,
                'is_dlr' => $is_dlr
            ];
            $message_id = $this->message->insert_by_array($message_param);
            if (empty($message_id)) {
                $this->db->trans_rollback();
                return ['status' => false, 'code' => 0, 'message' => "Message insert failed"];
            }
            //Insert to message details
            $batch_insert = $this->messagedetails->insert_batch_multi($message_id, $encrypt_number, $number_arr, $country_ids, $cost, $num_variable_message);
            if (empty($batch_insert)) {
                $this->db->trans_rollback();
                return ['status' => false, 'code' => 0, 'message' => "Message details insert failed"];
            }

            //Delete outbox message
            $outbox_info->delete();
            //Send successfully
            return ['status' => true, 'code' => 0, 'message' => "Message send successfully", 'message_id' => $message_id, 'type' => "regular"];
        }

        //Later message send
        if (!empty($outbox_info->sending_datetime)) {
            //Insert to message tb
            $message_param = [
                'user_id' => $outbox_info->user_id,
                'channel' => $outbox_info->channel,
                'text' => $outbox_info->text,
                'creation_datetime' => date("Y-m-d H:i:s"),
                'sending_datetime' => $outbox_info->sending_datetime,
                'variables_message' => $outbox_info->variables_message,
                'count' => $outbox_info->count,
                'cost' => $outbox_info->cost,
                'sender_name' => $outbox_info->sender_name,
                'auth_code' => $outbox_info->auth_code,
                'advertising' => $outbox_info->advertising,
                'is_dlr' => $is_dlr
            ];

            $message_id = $this->message->insert_by_array($message_param);
            if (empty($message_id)) {
                $this->db->trans_rollback();
                return ['status' => false, 'code' => 0, 'message' => "Message insert failed"];
            }

            //Insert to message details

            $batch_insert = $this->messagedetails->insert_batch_multi($message_id, $encrypt_number, $number_arr, $country_ids, $cost);
            if (empty($batch_insert)) {
                $this->db->trans_rollback();
                return ['status' => false, 'code' => 0, 'message' => "Message details insert failed"];
            }
            //Update message status to message                    
            if (!empty($outbox_info->variables_message) && empty($outbox_info->sending_datetime)) {
                $update_message = $this->message->update_by_array(array('status' => 1), array('id' => $message_id));
                if (empty($update_message)) {
                    $this->db->trans_rollback();
                    return ['status' => false, 'code' => 0, 'message' => "Message status update failed"];
                }
            }

            //Delete outbox message
            $outbox_info->delete();
            //Send successfully
            return ['status' => true, 'code' => 0, 'message' => "Later message send successfully", 'message_id' => $message_id, 'type' => "later"];
        }
    }

    // http://localhost/arsl_3/api.php?comm=sendsms_get_id&user=nobalaa&pass=123456&to=966500000000&message=lasttest&sender=sendername&date=2017-07-31&time=10:30
    public function sendsms_get_id()
    {
        $this->load->model('user');
        $this->load->model('sender');
        $this->load->model('messagedetails');
        $this->load->model('outbox');
        $this->load->library('curl');
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $to = $this->input->post_get('to');
        $message = $this->input->post_get('message');
        $sender_name = $this->input->post_get('sender');
        $sender = null;

        //Check prohibited words
        $bad_words = $this->site_settings['bad_words'];
        if (!empty($bad_words)) {
            $bad_words = array_filter(explode(",", $bad_words));
            foreach ($bad_words as $word) {
                if (strpos($message, $word) !== false) {
                    die('-220');
                }
            }
        }

        $user = $this->user->get_by_username($username);
        if (!empty($user) && !empty($sender_name) && password_verify($password, $user->password)) {
            //User id assign
            $user_id = $user->id;
            //Reseller account user id as parent id
            if (!empty($user->parent_id)) {
                $user_id = $user->parent_id;
            }

            $sender = $this->sender->get_by_user_id_name($user_id, $sender_name);
            if (!empty($sender) && $sender->status <> "1") {
                $sender = null;
            }

            //Reseller sender name issue fixed
            if (!empty($user->parent_id) && !in_array($sender->id, explode(",", $user->granted_sender_ids))) {
                $sender = null;
            }
        }

        $date = $this->input->post_get('date');
        $time = $this->input->post_get('time');
        if (empty($username) || empty($password) || empty($to) || empty($message) || empty($sender_name)) {
            echo "-100";
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo "-110";
        } elseif (!$user->active) {
            echo "-111";
        } elseif ($user->blocked) {
            echo "-112";
        } elseif (!$this->site_settings['site_sending_case']) {
            echo "-114";
        } elseif (!$user->unlimited_senders && empty($sender)) {
            echo "-115";
        } elseif (
            !preg_match('/^[0-9A-Za-z,.;:&? _@-]{2,11}+$/', $sender_name) &&
            !(is_numeric($sender_name) && strlen($sender_name) <= 14)
        ) {
            echo "-116";
        } else {
            $this->messagedetails->create_statistics_temp_table($user->id, $message, $to);
            $entries = $this->messagedetails->get_statistics($user->id);
            $total_cnt = 0;
            $total_cost = 0;
            if (!empty($entries)) {
                for ($i = 0; $i < count($entries); $i++) {
                    if ($entries[$i]["coverage_status"] == 1) {
                        $total_cnt += $entries[$i]["cnt"];
                        $total_cost += $entries[$i]["cost"];
                    }
                }
            }
            if ($total_cost == 0) {
                echo "-113";
            } elseif ($total_cost > ($user->total_balance - $user->spent_balance + $user->credit_limit)) {
                echo "-113";
            } else {
                $auth_code = rand(1000000, 9999999);
                if (empty($date)) {
                    $sending_time = null;
                } else {
                    if (empty($time)) {
                        $sending_time = $date;
                    } else {
                        $sending_time = $date . ' ' . $time;
                    }

                    //Valide date time check
                    if (DateTime::createFromFormat('Y-m-d G:i', $sending_time) === FALSE) {
                        $sending_time = null;
                    }
                }

                $entry_id = $this->outbox->insert_by_array(
                    array(
                        'id' => null,
                        'channel' => 'API',
                        'user_id' => $user->id,
                        'text' => $message,
                        'creation_datetime' => date("Y-m-d H:i:s"),
                        'sending_datetime' => $sending_time,
                        'repeation_period' => "0",
                        'repeation_times' => 0,
                        'variables_message' => 0,
                        'sender_name' => $sender_name,
                        'excel_file_numbers' => '',
                        'all_numbers' => $to,
                        'number_index' => 0,
                        'auth_code' => $auth_code
                    )
                );

                $response = $this->curl->_simple_call("get", site_url("user/sms/pre_flush_by_auth_code/{$entry_id}/{$auth_code}"), array(), array("TIMEOUT" => 3));
                $arr_res = json_decode($response, TRUE);
                echo "{$arr_res['data']['message_id']}:u";
            }

            $this->messagedetails->drop_statistics_temp_table($user->id);
        }
    }

    // http://localhost/arsl_3/api.php?comm=check_sms_status&user=nobalaa&pass=123456&message=56
    public function check_sms_status()
    {
        $this->load->model('user');
        $this->load->model('message');
        $this->load->library('curl');
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $message_id = $this->input->post_get('message');
        //Get user info
        $user = $this->user->get_by_username($username);
        //Check validation
        if (empty($username) || empty($password) || empty($message_id)) {
            die("-100");
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            die("-110");
        } elseif (!$user->active) {
            die("-111");
        } elseif ($user->blocked) {
            die("-112");
        }

        //Get message status
        $response = $this->message->get_message_by_id($message_id);
        if ($response['status'] == "error") {
            die("SMS Info not found");
        }

        //Readable status
        $status = NULL;
        if ($response['status'] == "success") {
            if ($response['data']['status'] == 0) {
                $status = "SMS is pending";
            } elseif ($response['data']['status'] == 1) {
                $status = "SMS is under processing";
            } elseif ($response['data']['status'] == 2) {
                $status = "SMS send successfully";
            }
        }

        die($status);
    }

    //http://localhost/arsl_3/api.php?comm=groupcontact&user=nobalaa&pass=123456&page=5&grpid=226
    public function groupcontact()
    {
        $this->load->model('user');
        $this->load->model('contactgroup');
        $this->load->library('encryption');
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $secret_key = $this->input->post_get('secret_key');
        $grpid = $this->input->post_get('grpid');
        $group = $this->contactgroup->get_by_id($grpid);
        $page = $this->input->post_get('page');
        $user = $this->user->get_by_username($username);
        $contacts = null;
        if (!empty($group) && !empty($page) && is_numeric($page) && $page > 0) {
            $contacts = $group->get_contacts(($page - 1) * 100, 100);
        }
        if (empty($username) || (empty($password) && empty($secret_key)) || empty($grpid) || empty($page)) {
            echo "<error><Code>-100</Code><Description>Missing parameters</Description></error>";
        } elseif (empty($user) || (!password_verify($password, $user->password) && $secret_key != $this->encryption->decrypt($user->secret_key))) {
            echo "<error><Code>-110</Code><Description>User name and password Wrong</Description></error>";
        } elseif (empty($group)) {
            echo "<error><Code>-113</Code><Description>Group  not for  user</Description></error>";
        } elseif (empty($contacts)) {
            echo "<error><Code>-112</Code><Description>No Contact for this User</Description></error>";
        } else {
            $res = "<usercontact>";
            foreach ($contacts as $contact) {
                $res .= "<contact>";
                $res .= "<id>{$contact->id}</id>";
                $res .= "<name>{$contact->name}</name>";
                $res .= "<mobile>{$contact->number}</mobile>";
                $res .= "</contact>";
            }
            $res .= "</usercontact>";
            echo $res;
        }
    }

    // http://localhost/arsl_3/api.php?comm=newgroup&user=user&pass=password&name=groupName&notes=GroupNotes
    public function newgroup()
    {
        $this->load->model('user');
        $this->load->model('contactgroup');
        $this->load->library('encryption');
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $secret_key = $this->input->post_get('secret_key');
        $user = $this->user->get_by_username($username);
        $name = $this->input->post_get('name');
        $notes = $this->input->post_get('notes');
        $parent = $this->input->post_get('parent');
        if (empty($parent)) {
            $parent = 0;
        } else {
            $parent_group = $this->contactgroup->get_by_id($parent);
        }
        if (empty($username) || (empty($password) && empty($secret_key))) {
            echo "-100";
        } elseif (empty($name)) {
            echo "-113";
        } elseif (empty($user) || (!password_verify($password, $user->password) && $secret_key != $this->encryption->decrypt($user->secret_key))) {
            echo "-110";
        } elseif (!$this->contactgroup->check_name_availability($name, $user->id)) {
            echo "-114";
        } elseif (!empty($parent) && empty($parent_group)) {
            echo "-115";
        } elseif (!empty($parent_group) && !empty($parent_group->group_id)) {
            echo "-116";
        } else {
            $this->contactgroup->insert_by_array(
                array(
                    'id' => null,
                    'user_id' => $user->id,
                    'group_id' => $parent,
                    'name' => $name,
                    'note' => $notes
                )
            );
            echo "999";
        }
    }

    //http://localhost/arsl_3/api.php?comm=newcontact&user=nobalaa&pass=123456&name=contactName&mobile=966000000&grpid=333
    public function newcontact()
    {
        $this->load->model('user');
        $this->load->model('contact');
        $this->load->model('Contactgroup');
        $this->load->library('encryption');
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $secret_key = $this->input->post_get('secret_key');
        $name = $this->input->post_get('name');
        $number = $this->input->post_get('mobile');
        $grpid = $this->input->post_get('grpid');
        //Check validation
        if (empty($username) || (empty($password) && empty($secret_key)) || empty($number)) {
            die('-100');
        }

        //Check number length
        if (!is_numeric($number) || strlen($number) < 8) {
            die('-111');
        }

        //Get user info
        $user = $this->user->get_by_username($username);
        if (empty($user) || (!password_verify($password, $user->password) && $secret_key != $this->encryption->decrypt($user->secret_key))) {
            die('-110');
        }

        //Handle group id issue
        if (!empty($grpid)) {
            //Check group id dependancy
            $get_group_info = $this->Contactgroup->get_by_id_user_id($grpid, $user->id);
            if (empty($get_group_info)) {
                die('-112');
            }
        }

        //Insert operation
        $insert_param = [
            'id' => null,
            'user_id' => $user->id,
            'group_id' => (!empty($get_group_info) ? $grpid : 0),
            'number' => $number,
            'name' => (!empty($name) ? $name : null)
        ];

        $this->contact->insert_by_array($insert_param);
        die('999');
    }

    //http://localhost/arsl_3/api.php?comm=deletegrp?user=nobalaa&pass=nob789&grpid=333
    public function deletegrp()
    {
        $this->load->model('user');
        $this->load->model('Contactgroup');
        $this->load->library('encryption');
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $secret_key = $this->input->post_get('secret_key');
        $grpid = $this->input->post_get('grpid');

        //Check validation
        if (empty($username) || (empty($password) && empty($secret_key)) || empty($grpid)) {
            die('-100');
        }

        //Get user info
        $user = $this->user->get_by_username($username);
        if (empty($user) || (!password_verify($password, $user->password) && $secret_key != $this->encryption->decrypt($user->secret_key))) {
            die('-110');
        }

        $get_group_info = $this->Contactgroup->get_by_id_user_id($grpid, $user->id);
        if (empty($get_group_info)) {
            die('-111');
        }

        $this->Contactgroup->get_by_id_user_id($grpid, $user->id)->delete();
        die('999');
    }

    //http://localhost/arsl_3/api.php?comm=usergroup&user=nobalaa&pass=123456
    public function usergroup()
    {
        $this->load->model('user');
        $this->load->library('encryption');
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $secret_key = $this->input->post_get('secret_key');
        if (empty($username) || (empty($password) && empty($secret_key))) {
            //Username or password missing
            die('-100');
        }

        //User Info
        $user = $this->user->get_by_username($username);
        if (empty($user) || (!password_verify($password, $user->password) && $secret_key != $this->encryption->decrypt($user->secret_key))) {
            //Username or password wrong
            die('-110');
        }

        //Get group info        
        $groups = $user->get_contact_groups();
        if (empty($groups)) {
            //Contact group not found
            die('-117');
        }

        $response = [];
        foreach ($groups as $key => $group) {
            $response[$key] = [
                'id' => $group['id'],
                'name' => $group['name'],
                'count' => $group['count'],
                'note' => $group['note']
            ];

            foreach ($group['groups'] as $sub_group) {
                $response[$key]['sub_group'] = [
                    'id' => $sub_group['id'],
                    'name' => $sub_group['name'],
                    'count' => $sub_group['count'],
                    'note' => $sub_group['note']
                ];
            }
        }

        return response_json($response);
    }

    //http://localhost/arsl_3/api.php?comm=usercontact&user=nobalaa&pass=123456&page=100000
    public function usercontact()
    {
        $this->load->model('user');
        $this->load->library('encryption');
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $secret_key = $this->input->post_get('secret_key');
        $page = $this->input->post_get('page');
        $user = $this->user->get_by_username($username);
        $contacts = null;
        if (!empty($page) && is_numeric($page) && $page > 0) {
            $contacts = $user->get_contacts($page, 100);
        }
        if (empty($username) || (empty($password) && empty($secret_key)) || empty($page)) {
            echo "<error><Code>-100</Code><Description>Missing parameters</Description></error>";
        } elseif (empty($user) || (!password_verify($password, $user->password) && $secret_key != $this->encryption->decrypt($user->secret_key))) {
            echo "<error><Code>-110</Code><Description>User name and password Wrong</Description></error>";
        } elseif (empty($contacts)) {
            echo "<error><Code>-117</Code><Description>No Contact for this User</Description></error>";
        } else {
            $res = "<usercontact>";
            foreach ($contacts as $contact) {
                $res .= "<contact>";
                $res .= "<id>{$contact['id']}</id>";
                $res .= "<name>{$contact['name']}</name>";
                $res .= "<mobile>{$contact['number']}</mobile>";
                $res .= "</contact>";
            }
            $res .= "</usercontact>";
            echo $res;
        }
    }

    // Mobile API
    // http://localhost/arsl_3/api.php?comm=add_app_installer&user=nobalaa&pass=123456
    public function add_app_installer()
    {
        $this->load->model('user');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $secret_key = $this->input->post_get('secret_key');
        $user = $this->user->get_by_username($username);
        header("Content-type: application/json");
        if (empty($username) || (empty($password) && empty($secret_key))) {
            echo json_encode(array("error" => true, "code" => -100, "desc" => "missing parameters"));
        } elseif (empty($user) || (!password_verify($password, $user->password) && $secret_key != $this->encryption->decrypt($user->secret_key))) {
            echo json_encode(array("error" => true, "code" => -110, "desc" => "invalid user name or password"));
        } else {
            $this->user->update_by_array(array("use_app" => 1), array("id" => $user->id));
            return response_json(["error" => false, "code" => 999, "desc" => "added successfully"]);
        }
    }

    // http://localhost/arsl_3/api.php?comm=add_notification_reg_id&user=nobalaa&pass=123456&regid=1
    public function add_notification_reg_id()
    {
        $this->load->model('user');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $secret_key = $this->input->post_get('secret_key');
        $regid = $this->input->post('regid');
        $user = $this->user->get_by_username($username);
        header("Content-type: application/json");
        if (empty($username) || (empty($password) && empty($secret_key)) || empty($regid)) {
            echo json_encode(array("error" => true, "code" => -100, "desc" => "missing parameters"));
        } elseif (empty($user) || (!password_verify($password, $user->password) && $secret_key != $this->encryption->decrypt($user->secret_key))) {
            echo json_encode(array("error" => true, "code" => -110, "desc" => "invalid user name or password"));
        } else {
            $this->load->model('usersnotificationregids');
            $this->db->query("DELETE FROM {$this->usersnotificationregids->table_name} WHERE usr_id='{$user->id}' OR reg_id='{$regid}'");
            $this->usersnotificationregids->insert_by_array(array('usr_id' => $user->id, 'id' => NULL, 'reg_id' => $regid));
            return response_json(["error" => false, "code" => 999, "desc" => "added successfully"]);
        }
    }

    // http://localhost/arsl_3/api.php?comm=api_confirm
    public function api_confirm()
    {
        header("Content-type: application/json");
        echo json_encode(array('error' => false, 'code' => 999, 'desc' => "Welcome To Our API"));
    }

    // http://localhost/arsl_3/api.php?comm=enter_length
    public function enter_length()
    {
        if (isset($this->site_settings['enter_length'])) {
            echo $this->site_settings['enter_length'];
        } else {
            echo 1;
        }
    }

    // http://localhost/arsl_3/api.php?comm=exportToGroup2&user=nobalaa&pass=123456&data=[{"mobile":"963966222197","name":"1"}, {"mobile":"963966222197","name":"2"}]
    public function exportToGroup2()
    {
        $this->load->model('user');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $data = $this->input->post('data');
        $user = $this->user->get_by_username($username);
        $contacts = json_decode($data, true);
        header("Content-type: application/json");
        if (empty($username) || empty($password) || empty($data)) {
            echo json_encode(array("error" => true, "code" => -100, "desc" => "missing parameters"));
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo json_encode(array("error" => true, "code" => -110, "desc" => "invalid user name or password"));
        } elseif (empty($contacts) || !is_array($contacts)) {
            echo json_encode(array("error" => true, "code" => -112, "desc" => "missing parameters"));
        } else {
            $this->load->model('contactgroup');
            $contactgroup_id = $this->contactgroup->insert_by_array(
                array(
                    'id' => null,
                    'group_id' => 0,
                    'user_id' => $user->id,
                    'name' => 'My Mobile Contacts',
                    'note' => 'My Mobile Contacts Last Updated on: ' . date("Y-m-d")
                )
            );
            $this->load->model('contact');
            for ($i = 0; $i < count($contacts); $i++) {
                $contacts[$i]['number'] = $contacts[$i]['mobile'];
            }
            $this->contact->import($contacts, $user->id, $contactgroup_id, false);
            $this->contact->repeation_cancelation($user->id, $contactgroup_id);
            echo json_encode(array('error' => false, 'code' => 999, 'valid' => count($contacts), 'invalid' => 0));
        }
    }

    public function sentsms()
    {
        $this->load->model('user');
        $this->load->model('message');
        $this->load->model('messagedetails');
        $this->load->library('pagination');

        $username = $this->input->post_get('user');
        $secret_key = $this->input->post_get('secret_key');
        $message_id = $this->input->post_get('message_id');
        $number = $this->input->post_get('number');
        $actual_link = (empty($_SERVER['HTTPS']) ? 'http' : 'https') . "://$_SERVER[HTTP_HOST]";
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            $parameters = $_POST;
        } else {
            $parameters = $_GET;
        }

        unset($parameters['pass'], $parameters['secret_key']);
        $api_log_id = $this->apilog->insert_by_array(
            array(
                'url' => $actual_link,
                'parameters' => json_encode($parameters),
                'ip' => get_client_ip(),
                'method' => $_SERVER['REQUEST_METHOD'],
            )
        );

        if (empty($username) || empty($message_id)) {//|| empty($secret_key)
            //User name,Password,To,Message and Sender name required
            $this->apilog->update_by_array(array('status' => -100, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
            die('-100');
        }

        $user_info = $this->user->get_by_username($username);
        $this->load->library('encryption');
        // if (empty($user_info) || !password_verify($password, $user_info->password)) {
        if (empty($user_info)) {// || $secret_key != $this->encryption->decrypt($user_info->secret_key)

            //User information not found
            $this->apilog->update_by_array(array('status' => -110, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
            die('-110');
        }
        $message = $this->message->get_by_id($message_id);
        if (!empty($message)) {
            $message_user = $this->user->get_by_id($message->user_id);
            if ($message_user->id == $user_info->id || $message_user->parent_id == $user_info->id) {
                $rowsPerPage = isset($_GET['perPage']) ? $_GET['perPage'] : 25;
                if ($rowsPerPage > 100) {
                    $rowsPerPage = 100;
                }
                $current_page = isset($_GET['page']) ? $_GET['page'] : 1;
                $startRow = ($current_page - 1) * $rowsPerPage;

                $data = $this->messagedetails->load_data_sms_api($message_id, ['number' => $number, 'iDisplayStart' => $startRow, 'iDisplayLength' => $rowsPerPage, 'iSortCol_0' => 0, 'sSortDir_0' => 'asc']);
                $totalPages = ceil($data['count'] / 25);

                $current_page = max(1, min($current_page, $totalPages));
                $nextPage = $current_page + 1;
                if ($nextPage > $totalPages) {
                    $nextPage = $totalPages; // Set to the last page if there's no next page
                }
                $previousPage = $current_page - 1;
                if ($previousPage < 1) {
                    $previousPage = 1; // Set to the first page if there's no previous page
                }
                header('Content-Type: application/json');
                echo json_encode(['status' => 'success', 'messages' => $data['data'], 'pageNumber' => (int) $current_page, 'nextPage' => $nextPage, 'previousPage' => $previousPage, 'totalPages' => $totalPages]);
            }
            // $data = json_decode($this->messagedetails->load_data_sms($message_id,  ['iDisplayStart'=>'0','iDisplayLength'=>25,'iSortCol_0'=>0]));

        }
    }

    // http://localhost/arsl_3/api.php?comm=get_app_version&type=0 
    public function get_app_version()
    {
        $this->load->model('versions');
        $type = $this->input->post('type');
        $versions = $this->versions->get_by_type($type);
        header("Content-type: application/json");
        if (!isset($type)) {
            echo json_encode(array("error" => true, "code" => -100, "desc" => "missing parameters"));
        } elseif (empty($versions)) {
            echo json_encode(array("error" => true, "code" => -110, "desc" => "request error or wrong type"));
        } else {
            return response_json(["error" => false, "code" => 999, "version" => $versions->to_array()]);
        }
    }

    // http://localhost/arsl_3/api.php?comm=get_mobile_card&user=nobalaa&pass=123456
    public function get_mobile_card()
    {
        $this->load->model('user');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $user = $this->user->get_by_username($username);
        header("Content-type: application/json");
        if (empty($username) || empty($password)) {
            echo json_encode(array("error" => true, "code" => -100, "desc" => "missing parameters"));
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo json_encode(array("error" => true, "code" => -110, "desc" => "invalid user name or password"));
        } else {
            echo json_encode(array("error" => true, "code" => -102, "text" => "No cards to show"));
        }
    }

    // http://localhost/arsl_3/api.php?comm=get_one_card_data&user=nobalaa&pass=123456
    public function get_one_card_data()
    {
        $this->load->model('user');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $user = $this->user->get_by_username($username);
        header("Content-type: application/json");
        if (empty($username) || empty($password)) {
            echo json_encode(array("error" => true, "code" => -100, "desc" => "missing parameters"));
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo json_encode(array("error" => true, "code" => -110, "desc" => "invalid user name or password"));
        } else {
            echo json_encode(array("error" => true, "code" => -102, "text" => "No cards found"));
        }
    }

    // http://localhost/arsl_3/api.php?comm=mobile_bnk_chrg&user=nobalaa&pass=123456&points=100&depositAmount=100&accountnumber=123456&accountname=abdou&depositdate=12-12-2017&
    // bankname=rajihi&paymentway=BANK&details=no details
    public function mobile_bnk_chrg()
    {
        $this->load->model('user');
        $this->load->model('payplan');
        $check_payplan = false;
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $points_cnt = $this->input->post('points');
        $amount = $this->input->post('depositamount');
        $bnk_account_num = $this->input->post('accountnumber');
        $bnk_account_name = $this->input->post('accountname');
        $deposit_date = $this->input->post('depositdate');
        $bank_name = $this->input->post('bankname');
        $payment_way = $this->input->post('paymentway');
        $details = $this->input->post('details');
        $currency = 'SAR';
        $user = $this->user->get_by_username($username);
        $payplan = $this->payplan->get_by_price_currency_method($amount, $currency, 'BANK');
        if (!empty($payplan) && $payplan->points_cnt == $points_cnt) {
            $check_payplan = true;
        }
        header("Content-type: application/json");
        if (empty($username) || empty($password) || empty($points_cnt) || empty($amount) || !$check_payplan) {
            echo json_encode(array("error" => true, "code" => -100, "desc" => "missing parameters"));
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo json_encode(array("error" => true, "code" => -110, "desc" => "invalid user name or password"));
        } else {
            $obj_data = array(
                'id' => null,
                'user_id' => $user->id,
                'points_cnt' => $payplan->points_cnt,
                'amount' => $payplan->price,
                'currency' => $payplan->currency,
                'bank_name' => $bank_name,
                'account_number' => $bnk_account_num,
                'account_owner_name' => $bnk_account_name,
                'deposit_date' => $deposit_date,
                'request_date' => date_format(new DateTime(), 'Y-m-d h:i')
            );
            $this->load->model('chargerequestbank');
            $id = $this->chargerequestbank->insert_by_array($obj_data);
            if ($this->site_settings['admin_charging_request'] == "EMAIL" || $this->site_settings['admin_charging_request'] == "BOTH") {
                $this->send_mail(
                    $this->site_settings['site_email'],
                    $this->site_settings['site_name'],
                    $this->site_settings['receiver_email'],
                    $this->lang->line('lbl_charging_request'),
                    $this->lang->line('msg_info_messages_charging_request'),
                    array('{site_name}' => $this->site_settings['site_name'], '{username}' => $user->username)
                );
            }
            if ($this->site_settings['admin_charging_request'] == "SMS" || $this->site_settings['admin_charging_request'] == "BOTH") {
                $this->send_sms_admin(
                    $this->site_settings['system_sms_sender'],
                    $this->site_settings['receiver_number'],
                    strip_tags($this->lang->line('msg_info_messages_charging_request')),
                    0,
                    array('{site_name}' => $this->site_settings['site_name'], '{username}' => $user->username)
                );
            }
            echo "999";
        }
    }

    // http://localhost/arsl_3/api.php?comm=virtual_numbers&user=nobalaa&pass=123456&cardSerial=123456&cardCode=098765
    public function mobile_card_charge()
    {
        $this->load->model('user');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $card_serial = $this->input->post('cardserial');
        $card_code = $this->input->post('cardcode');
        $user = $this->user->get_by_username($username);
        header("Content-type: application/json");
        if (empty($username) || empty($password) || empty($card_serial) || empty($card_code)) {
            echo json_encode(array("response" => "error", "text" => "Missing parameters"));
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo json_encode(array("response" => "error", "text" => "Invalid user name or password"));
        } else {
            echo json_encode(array("response" => "error", "text" => "Missing parameters"));
        }
    }

    // http://localhost/arsl_3/api.php?comm=mobile_ticket&user=nobalaa&pass=123456&message=this is new message from this user
    public function mobile_ticket()
    {
        $this->load->model('user');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $message = $this->input->post('message');
        $user = $this->user->get_by_username($username);
        header("Content-type: application/json");
        if (empty($username) || empty($password) || empty($message)) {
            echo json_encode(array("error" => true, "code" => -100, "desc" => "Missing parameters"));
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo json_encode(array("error" => true, "code" => -101, "desc" => "Wrong user name or password"));
        } else {
            $this->load->model('ticket');
            $id = $this->ticket->insert_by_array(array('id' => null, 'user_id' => $user->id, 'title' => 'Support Ticket from Mobile App', 'content' => $message, 'date' => date("Y-m-d H:i")));
            return response_json(["error" => false, "code" => 999, "desc" => "adding successfully"]);
        }
    }

    // http://localhost/arsl_3/api.php?comm=mobilemyinfo&user=nobalaa&pass=123456
    public function mobilemyinfo()
    {
        $this->load->model('user');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $user = $this->user->get_by_username($username);
        header("Content-type: application/json");
        if (empty($username) || empty($password)) {
            echo json_encode(array("error" => true, "code" => -100));
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo json_encode(array("error" => true, "code" => -110));
        } else {
            $this->load->model('country');
            $country = $this->country->get_by_id($user->country_id);
            $res = array(
                "error" => false,
                "userdata" => array(
                    'userid' => $user->id,
                    'username' => $user->username,
                    'name' => $user->name,
                    'email' => $user->email,
                    'mobile' => $user->number,
                    'country' => $country->name_en,
                    'address' => $user->address,
                    'activate' => empty($user->active) ? "No" : "Yes"
                )
            );

            return response_json($res);
        }
    }

    // http://localhost/arsl_3/api.php?comm=mobileusercontact&user=nobalaa&pass=123456
    public function mobileusercontact()
    {
        $this->load->model('user');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $page = $this->input->get('page');
        if (empty($page)) {
            $page = 0;
        }
        $user = $this->user->get_by_username($username);
        $this->load->model('contact');
        if (!empty($user)) {
            $contacts = $this->contact->get_by_user_id($user->id, $page * 100, 100);
        }
        header("Content-type: application/json");
        if (empty($username) || empty($password)) {
            echo json_encode(array("error" => -100));
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo json_encode(array("error" => -110));
        } elseif (empty($contacts)) {
            echo json_encode(array("error" => -112));
        } else {
            return response_json(["error" => false, "contacts" => $contacts]);
        }
    }

    // http://localhost/arsl_3/api.php?comm=mobileusergroup&user=nobalaa&pass=123456
    public function mobileusergroup()
    {
        $this->load->model('user');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $user = $this->user->get_by_username($username);
        $this->load->model('contact');
        if (!empty($user)) {
            $groups = $user->get_contact_groups();
        }
        header("Content-type: application/json");
        if (empty($username) || empty($password)) {
            echo json_encode(array("error" => true, "code" => -100));
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo json_encode(array("error" => true, "code" => -110));
        } elseif (empty($groups)) {
            echo json_encode(array("error" => true, "code" => -117));
        } else {
            $res = array('error' => false);
            $res['groups'] = array();
            $groups = array_values($groups);
            for ($i = 0; $i < count($groups); $i++) {
                $groups[$i]['numberofcontact'] = $groups[$i]['count'];
                if (!empty($groups[$i]['groups'])) {
                    for ($j = 0; $j < count(0); $j++) {
                        $groups[$i]['groups'][$j]['numberofcontact'] = $groups[$i]['groups'][$j]['count'];
                    }
                    $groups[$i]['subgroups'] = $groups[$i]['groups'];
                }
            }
            return response_json(["error" => false, "groups" => $groups]);
        }
    }

    // http://localhost/arsl_3/api.php?comm=mobileusersender&user=nobalaa&pass=123456
    public function mobileusersender()
    {
        $this->load->model('user');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $user = $this->user->get_by_username($username);
        $this->load->model('sender');
        if (!empty($user)) {
            $senders = $user->get_active_senders();
        }
        header("Content-type: application/json");
        if (empty($username) || empty($password)) {
            echo json_encode(array("error" => true, "code" => -100));
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo json_encode(array("error" => true, "code" => -110));
        } elseif (empty($senders)) {
            echo json_encode(array("error" => true, "code" => -117, "default" => $this->site_settings['default_sender'], "description" => "No Sender for This User"));
        } else {
            for ($i = 0; $i < count($senders); $i++) {
                $senders[$i]['text'] = $senders[$i]['name'];
                $senders[$i]['status'] = sender_status($senders[$i]['status']);
                //$senders[$i]['status'] = ($senders[$i]['status'] == 0 ? "UnActive" : ($senders[$i]['status'] == 1 ? "Active" : "Reject"));
                $senders[$i]['default'] = ($senders[$i]['default'] == 0 ? "false" : "true");
            }
            return response_json(["error" => false, "senders" => $senders]);
        }
    }

    // http://localhost/arsl_3/api.php?comm=notifications&user=nobalaa&pass=123456
    public function notifications()
    {
        $this->load->model('user');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $user = $this->user->get_by_username($username);
        $this->load->model('usersnotifications');
        if (!empty($user) && password_verify($password, $user->password)) {
            $notifications = $this->usersnotifications->get_by_user_id($user->id);
        }
        header("Content-type: application/json");
        if (empty($username) || empty($password)) {
            echo json_encode(array("error" => true, "code" => -100, "description" => "Missing parameters"));
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo json_encode(array("error" => true, "code" => -110, "description" => "User name and password Wrong"));
        } elseif (empty($notifications)) {
            echo json_encode(array("error" => true, "code" => -111, "description" => "no notifications found"));
        } else {
            return response_json(["error" => false, "code" => 999, "data" => $notifications]);
        }
    }

    // http://localhost/arsl_3/api.php?comm=payplans&user=nobalaa&pass=123456
    public function payplans()
    {
        $this->load->model('user');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $user = $this->user->get_by_username($username);
        header("Content-type: application/json");
        if (empty($username) || empty($password)) {
            echo json_encode(array("error" => true, "code" => -100, "description" => "Missing parameters"));
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo json_encode(array("error" => true, "code" => -110, "description" => "User name and password Wrong"));
        } else {
            $this->load->model('payplan');
            $payplans = $this->payplan->get_bank_as_array();
            for ($i = 0; $i < count($payplans); $i++) {
                $payplans[$i]['point_count'] = $payplans[$i]['points_cnt'];
            }

            return response_json($payplans);
        }
    }

    // http://localhost/arsl_3/api.php?comm=prices_imgs
    public function prices_imgs()
    {
        header("Content-type: application/json");
        $pricesimgs = array();

        if (!empty($this->site_settings['apps_prices_ar'])) {
            $pricesimgs[] = array('id' => 1, 'type' => 'ar', 'url' => $this->site_settings['apps_prices_ar']);
        }

        if (!empty($this->site_settings['apps_prices_en'])) {
            $pricesimgs[] = array('id' => 2, 'type' => 'en', 'url' => $this->site_settings['apps_prices_en']);
        }

        if (empty($pricesimgs)) {
            echo json_encode(array("error" => true, "code" => -102, "text" => "No imgs"));
        } else {
            return response_json(["error" => false, "code" => 999, "text" => "imgs found", "imgs" => $pricesimgs]);
        }
    }

    // http://localhost/arsl_3/api.php?comm=reset_psw_mobile&user=nobalaa&mobile=963966222197
    public function reset_psw_mobile()
    {
        $username = $this->input->post('user');
        $number = $this->input->post('mobile');
        $password = $this->input->post('pass');
        $this->load->model('user');
        $user = $this->user->get_by_username($username);
        if ($username == "" || $number == "") {
            echo -100;
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo -110;
        } else {
            $new_password = $user->reset_password();
            $this->load->model('announcement');
            $announcement = $this->announcement->get_by_name('retreiving_password');
            if ($announcement->media == "SMS" || $announcement->media == "BOTH") {
                if ($announcement->budget == "ADMIN") {
                    $this->send_sms_admin(
                        $this->site_settings['system_sms_sender'],
                        $user->number,
                        $announcement->text_sms,
                        0,
                        array(
                            '{site_name}' => $this->site_settings['site_name'],
                            '{username}' => $user->username,
                            '{password}' => $new_password
                        )
                    );
                } else {
                    $this->send_sms_user(
                        $user->id,
                        $this->site_settings['system_sms_sender'],
                        $user->number,
                        $announcement->text_sms,
                        0,
                        array(
                            '{site_name}' => $this->site_settings['site_name'],
                            '{username}' => $user->username,
                            '{password}' => $new_password
                        )
                    );
                }
            }
            echo 999;
        }
    }

    // http://localhost/arsl_3/api.php?comm=sendsmsmobilejson&user=nobalaa&pass=123456&
    // to=963966222197,963966222198&groups=1,2,3&message=Test Message&sender=Sender 6&date=2017-10-08&time=09:00

    public function sendsmsmobilejson()
    {
        $this->load->model('user');
        $this->load->model('sender');
        $this->load->model('messagedetails');
        $this->load->model('outbox');
        $this->load->library('curl');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $to = $this->input->post('to');
        $groups = $this->input->post('groups');
        if (!empty($groups)) {
            $to .= "G" . str_replace(',', 'G,', $groups);
        }
        $message = $this->input->post('message');
        $sender_name = $this->input->post('sender');
        $user = $this->user->get_by_username($username);
        $sender = null;
        if (!empty($user) && !empty($sender_name) && password_verify($password, $user->password)) {
            $sender = $this->sender->get_by_user_id_name($user->id, $sender_name);
            if ($sender->status <> "1") {
                $sender = null;
            }
        }
        $date = $this->input->post('date');
        $time = $this->input->post('time');
        if (empty($username) || empty($password) || empty($to) || empty($message) || empty($sender_name)) {
            echo json_encode(array('error' => true, 'code' => -100, 'desc' => "missing parameters"));
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo json_encode(array('error' => true, 'code' => -110, 'desc' => "invalid user name or password"));
        } elseif (!$user->active) {
            echo json_encode(array('error' => true, 'code' => -111, 'desc' => "account not activated"));
        } elseif ($user->blocked) {
            echo json_encode(array('error' => true, 'code' => -112, 'desc' => "blocked user"));
        } elseif (!$this->site_settings['site_sending_case']) {
            echo json_encode(array('error' => true, 'code' => -114, 'desc' => "site sending case"));
        } elseif (!$user->unlimited_senders && empty($sender)) {
            echo json_encode(array('error' => true, 'code' => -115, 'desc' => "invalid sender"));
        } elseif (
            !preg_match('/^[0-9A-Za-z,.;:&? _@-]{2,11}+$/', $sender_name) &&
            !(is_numeric($sender_name) && strlen($sender_name) <= 14)
        ) {
            echo json_encode(array('error' => true, 'code' => -115, 'desc' => "invalid sender"));
        } else {
            $push_url = "http://{$_SERVER['SERVER_NAME']}/api.php?comm=push";
            $this->messagedetails->create_statistics_temp_table($user->id, $message, $to);
            $entries = $this->messagedetails->get_statistics($user->id);
            $total_cnt = 0;
            $total_cost = 0;
            if (!empty($entries)) {
                for ($i = 0; $i < count($entries); $i++) {
                    if ($entries[$i]["coverage_status"] == 1) {
                        $total_cnt += $entries[$i]["cnt"];
                        $total_cost += $entries[$i]["cost"];
                    }
                }
            }
            if ($total_cost == 0) {
                echo json_encode(array('error' => true, 'code' => -100, 'desc' => "missing parameters"));
            } elseif ($total_cost > ($user->total_balance - $user->spent_balance + $user->credit_limit)) {
                $title = "Message failed";
                $msgg = "Your message failed to send using Sms Plus service! + Reason: It seems that you are sending from a website or another phone.";
                $fields = array('to' => $user->id, 'title' => $title, 'message' => $msgg);
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $push_url);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
                $out = curl_exec($ch);
                curl_close($ch);
            } else {
                $auth_code = rand(1000000, 9999999);
                if (empty($date)) {
                    $sending_time = null;
                } else {
                    if (empty($time)) {
                        $sending_time = $date;
                    } else {
                        $sending_time = $date . ' ' . $time;
                    }
                    if (DateTime::createFromFormat('Y-m-d G:i:s', $sending_time) === FALSE) {
                        $sending_time = null;
                    }
                }
                $entry_id = $this->outbox->insert_by_array(
                    array(
                        'id' => null,
                        'channel' => 'MOBILE',
                        'user_id' => $user->id,
                        'text' => $message,
                        'creation_datetime' => date("Y-m-d H:i:s"),
                        'sending_datetime' => $sending_time,
                        'repeation_period' => "0",
                        'repeation_times' => 0,
                        'variables_message' => 0,
                        'sender_name' => $sender_name,
                        'excel_file_numbers' => '',
                        'all_numbers' => $to,
                        'number_index' => 0,
                        'auth_code' => $auth_code
                    )
                );
                $this->curl->_simple_call("get", site_url("user/sms/pre_flush_by_auth_code/{$entry_id}/{$auth_code}"), array(), array("TIMEOUT" => 3));

                $title = "Message sent successfully";
                $msgg = "Your message has been registered in outbox and it will be sent soon, numbers count: {$total_cnt} and messages cost: {$total_cost}";
                $fields = array('to' => $user->id, 'title' => $title, 'message' => $msgg);
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $push_url);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
                $out = curl_exec($ch);
                curl_close($ch);
            }
            $this->messagedetails->drop_statistics_temp_table($user->id);
        }
    }

    // https://localhost/arsl_3/api.php?comm=push
    public function push()
    {
        $to = $this->input->post('to');
        $message = $this->input->post('message');
        $message = html_entity_decode($message);
        $title = $this->input->post('title');
        $sender = $this->input->post('sender');
        $date = date("Y/m/d h:i:sa");
        if (empty($to) && empty($title)) {
            echo "missing param";
        } elseif ($to != "" && is_numeric($to)) {
            $this->load->model('usersnotifications');
            $this->usersnotifications->insert_by_array(
                array(
                    'title' => $title,
                    'message' => $message,
                    'date' => $date,
                    'type' => 1,
                    'usr_id' => $to,
                    'sender' => $this->site_settings['system_sms_sender']
                )
            );
            $push = array('message' => $message, 'title' => $title, 'type' => 1, 'date' => $date, 'sender' => $this->site_settings['system_sms_sender']);
            $this->load->model('usersnotificationregids');
            $usersnotificationregids = $this->usersnotificationregids->get_by_user_id($to);
            if (!empty($usersnotificationregids)) {
                $this->load->library('pushnotification');
                $this->pushnotification->sendPushNotificationToOne($usersnotificationregids[0]['reg_id'], $push);
            }
        }
    }

    // http://localhost/arsl_3/api.php?comm=virtual_numbers&user=nobalaa&pass=123456
    public function virtual_numbers()
    {
        $this->load->model('user');
        $username = $this->input->post('user');
        $password = $this->input->post('pass');
        $user = $this->user->get_by_username($username);
        $this->load->model('virtualnumbers');
        $virtualnumbers = $this->virtualnumbers->get_active_as_array();
        header("Content-type: application/json");
        if (empty($username) || empty($password)) {
            echo json_encode(array("error" => true, "code" => -100, "desc" => "missing parameters"));
        } elseif (empty($user) || !password_verify($password, $user->password)) {
            echo json_encode(array("error" => true, "code" => -110, "desc" => "invalid user name or password"));
        } elseif (empty($virtualnumbers)) {
            echo json_encode(array("error" => true, "code" => -111, "desc" => "no numbers found"));
        } else {
            $response['data'] = array();
            foreach ($virtualnumbers as $virtualnumber) {
                $response['data'][] = array('number' => $virtualnumber['number'], 'active' => $virtualnumber['active']);
            }
            $response['error'] = false;
            $response['code'] = 999;
            return response_json($response);
        }
    }

    public function getHeaders()
    {
        $headers = array();
        foreach ($_SERVER as $k => $v) {
            if (substr($k, 0, 5) == "HTTP_") {
                $k = str_replace('_', ' ', substr($k, 5));
                $k = str_replace(' ', '-', ucwords(strtolower($k)));
                $headers[$k] = $v;
            }
        }
        return $headers;
    }

    public function dlr()
    {
        die();
        $this->load->model('message');
        $data = $this->message->get_number_dlr();

        if ($data != null) {
            $userIds = array_unique(array_column($data, 'user_id'));

            $this->load->model('Userwebhook');
            $web_hooks = $this->Userwebhook->get_by_users_id($userIds);


            $webhookMapping = [];
            foreach ($web_hooks as $item) {
                $webhookMapping[$item['user_id']] = $item['webhook_url'];
            }

            foreach ($data as $item) {
                $user_id = $item['user_id'];
                $combinedArray[$user_id][] = [
                    'id' => $item['id'],
                    'number' => $item['number'],
                    'status' => $item['status'],
                    'message_id' => $item['message_id'],
                ];
            }

            $ch = array();
            $mh = curl_multi_init();
            $urls = array();

            foreach ($combinedArray as $index => $combined) {
                if (isset($webhookMapping[$index]) && $webhookMapping[$index] != null) {
                    $post = [
                        'data' => json_encode($combined)
                    ];
                    $webhook_url = $webhookMapping[$index];
                    $ch[$index] = curl_init();
                    curl_setopt($ch[$index], CURLOPT_URL, $webhook_url);
                    curl_setopt($ch[$index], CURLOPT_POST, true);
                    //  curl_setopt($ch[$index], CURLOPT_HEADER, 0);
                    curl_setopt($ch[$index], CURLOPT_POSTFIELDS, $post);
                    curl_setopt($ch[$index], CURLOPT_RETURNTRANSFER, true);
                    curl_multi_add_handle($mh, $ch[$index]);
                }

            }

            $active = null;
            for (; ; ) {
                curl_multi_exec($mh, $active);
                if ($active < 1) {
                    break;
                } else {
                    curl_multi_select($mh, 1);
                }
            }
            foreach ($ch as $index => $c) {
                $http_code = curl_getinfo($c)['http_code'];
                if ($http_code == 200) {
                    $ids = array_column($combinedArray[$index], 'id');
                    $this->message->update_number_send_dlr($ids);
                }

            }
            curl_multi_close($mh);
        }

    }

    public function webhook()
    {
        $json_data = json_encode($this->input->post());
        file_put_contents('api.log', $json_data . "\n", FILE_APPEND);
        header("Content-type: application/json");
        return response_json(['status' => '1']);
    }




    ////////////////////8/12/2023////atif

    public function new_test()
    {


        $ch = array();
        $mh = curl_multi_init();
        $urls = "https://www.dreams.sa/index.php/api/sendsms/?user=motaz2&secret_key=fa506acda64e71f9f24247dad70690a36976e6f2f4782412980ff2b0e762e29f&to=966597472790&message=newtest&sender=virtual";

        for ($i = 0; $i < 1000; $i++) {
            $ch = curl_init();

            // set URL and other appropriate options
            curl_setopt($ch, CURLOPT_URL, $urls);
            curl_setopt($ch, CURLOPT_HEADER, 0);

            // grab URL and pass it to the browser
            curl_exec($ch);

            // close cURL resource, and free up system resources
            curl_close($ch);
        }


    }


    /////////////////////// test with bulk 1/1/2024

    public function new_test_bulk()
    {

        $ch = array();
        $mh = curl_multi_init();
        $urls = array();

        for ($i = 0; $i < 10; $i++) {
            array_push($urls, "https://www.dreams.sa/index.php/api/sendsms/?user=9xdebugger&secret_key=da05140a0f5059e78da0e98bc7c3223dbeac16f29bbb14f98d56fb14f6a72cdc&to=597472790&message=bulktestnewdream&sender=Dreams");
        }


        foreach ($urls as $index => $url) {
            $ch[$index] = curl_init();
            curl_setopt($ch[$index], CURLOPT_URL, $url);
            curl_setopt($ch[$index], CURLOPT_HEADER, 0);
            curl_setopt($ch[$index], CURLOPT_RETURNTRANSFER, true);
            curl_multi_add_handle($mh, $ch[$index]);
        }

        $active = null;
        for (; ; ) {
            curl_multi_exec($mh, $active);
            if ($active < 1) {
                break;
            } else {
                curl_multi_select($mh, 1);
            }
        }
        foreach ($ch as $index => $c) {
            $data = json_decode(curl_multi_getcontent($c));
            $http_code = curl_getinfo($c)['http_code'];
            echo $http_code;
            curl_multi_remove_handle($mh, $c);
        }
        curl_multi_close($mh);


    }




    //////////////////////////////12/2/2024////////

    public function sendlinksms()
    {
        $this->load->model('user');
        $this->load->model('sender');
        $this->load->model('messagedetails');
        $this->load->model('outbox');
        $this->load->model('operator');
        $this->load->library('curl');
        $actual_link = (empty($_SERVER['HTTPS']) ? 'http' : 'https') . "://$_SERVER[HTTP_HOST]";
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            $parameters = $_POST;
        } else {
            $parameters = $_GET;
        }
        unset($parameters['pass'], $parameters['secret_key']);
        $api_log_id = $this->apilog->insert_by_array(
            array(
                'url' => $actual_link,
                'parameters' => json_encode($parameters),
                'ip' => get_client_ip(),
                'method' => $_SERVER['REQUEST_METHOD']
            )
        );
        //Parameter assign
        $username = $this->input->post_get('user');
        $password = $this->input->post_get('pass');
        $secret_key = $this->input->post_get('secret_key');
        $to = $this->input->post_get('to');
        $message = $this->input->post_get('message');
        $sender_name = $this->input->post_get('sender');
        $date = $this->input->post_get('date');
        $time = $this->input->post_get('time');
        $is_dlr = $this->input->post_get('is_dlr');
        $link = $this->input->post_get('link');


        if (!empty($link)) {
            $userid = $this->user->get_by_username($username);
            $userdomain = str_replace("https://", "", "$userid->domain");
            $profiledomain = '/' . $userdomain . '/';
            if ($userid->linksshortcut == 1 && preg_match($profiledomain, $link)) {
                $shortlinks = $this->shorturl($userid->id, $link);
                $shorturl = $shortlinks['url'];
                $short_id = $shortlinks['id'];
            } else {
                if ($userid->linksshortcut == 0) {
                    dd('user not subscribe in Short link service');

                }
                if ($userid->linksshortcut == 1 && preg_match($profiledomain, $link) != 1) {
                    dd('please write your rigth link from your profile');

                }
            }
            $message .= ' ' . $shorturl;
        }

        if (empty($is_dlr)) {
            $is_dlr = 0;
        }
        //Check validation
        if (empty($username) || (empty($password) && empty($secret_key)) || empty($to) || empty($message) || empty($sender_name)) {
            //User name,Password,To,Message and Sender name required
            $this->apilog->update_by_array(array('status' => -100, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
            die('-100');
        }

        //Check prohibited words
        $bad_words = $this->site_settings['bad_words'];
        if (!empty($bad_words)) {
            $bad_words = array_filter(explode(",", $bad_words));
            foreach ($bad_words as $word) {
                if (strpos($message, $word) !== false) {
                    //Check your message. There some prohibited words
                    $this->apilog->update_by_array(array('status' => -220, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
                    die('-220');
                }
            }
        }

        //Get user information
        $user_info = $this->user->get_by_username($username);
        $this->load->library('encryption');
        // if (empty($user_info) || !password_verify($password, $user_info->password)) {
        if (empty($user_info) || (!password_verify($password, $user_info->password) && $secret_key != $this->encryption->decrypt($user_info->secret_key))) {

            //User information not found
            $this->apilog->update_by_array(array('status' => -110, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
            die('-110');
        }

        //Check active user
        if (!$user_info->active) {
            //User is not active
            $this->apilog->update_by_array(array('status' => -111, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
            die('-111');
        }

        //Check block user
        if ($user_info->blocked) {
            //User is block
            $this->apilog->update_by_array(array('status' => -112, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
            die('-112');
        }

        $this->load->model('Whitelistip');
        $client_ip = get_client_ip();
        $valid_ip = $this->Whitelistip->get_by_user_id_ip($user_info->id, $client_ip);
        if (!$valid_ip) {
            $this->apilog->update_by_array(array('status' => -124, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
            die('-124');
        }


        //Check side sending case
        if (!$this->site_settings['site_sending_case']) {
            //Side sending case not found
            $this->apilog->update_by_array(array('status' => -114, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
            die('-114');
        }

        //Check sender name dependency
        if (!preg_match('/^[0-9A-Za-z,.;: ?_@-]{2,11}+$/', $sender_name) && !(is_numeric($sender_name) && strlen($sender_name) <= 14)) {
            //Sender dependancy found
            $this->apilog->update_by_array(array('status' => -116, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
            die('-116');
        }

        //User id assign
        $user_id = $user_info->id;

        //Reseller account user id as parent id
        if (!empty($user_info->parent_id)) {
            if ($this->user->get_by_id($user_info->parent_id)->is_hidden == 0)
                $user_id = $user_info->parent_id;
        }



        $sender = $this->sender->get_by_user_id_name($user_id, $sender_name);

        if (!empty($sender) && $sender->status != "1") {
            $sender = null;
        }


        //Reseller sender name issue fixed
        // if (!empty($user_info->parent_id) && !in_array($sender->id, explode(",", $user_info->granted_sender_ids))) {
        //     $sender = null;
        // }

        //Check unlimited sender
        if (!$user_info->unlimited_senders && empty($sender)) {
            //Unlimited sender issue found
            die('-115');
        }

        if (strpos($sender_name, '-AD')) {
            $offsettime = $this->site_settings['offset_time'];
            $server_time = date("H:i:s", strtotime("now -$offsettime minutes"));
            if (strtotime($server_time) > strtotime("22:00:00") || strtotime($server_time) < strtotime("09:00:00")) {
                $this->apilog->update_by_array(array('status' => -119, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
                die('-119');
            }
        }

        $advertising_words = false;
        if ($this->check_advertising_words_message($message) && !strpos($sender_name, '-AD')) {
            $advertising_words = true;
        }

        if ($this->check_url_in_message($message) && $user_info->allow_url == 0) {
            $advertising_words = true;
        }


        //Get active country info
        $this->load->model('country');
        $get_countries = $this->country->get_active_countries_by_user_v2($user_id, $user_info->is_international);
        $countries = array_column($get_countries, 'price', 'id');
        $operators = $this->operator->get_active_operators(array_keys($countries));
        $country_min_number_count = array_column($get_countries, 'min_number_count', 'id');
        $country_max_number_count = array_column($get_countries, 'max_number_count', 'id');

        //Get message length
        $this->load->helper('message');
        $message_long = calc_message_length($message, $this->setting->get_value_by_name('enter_length'));
        $all_numbers = array_filter(explode(',', $to));
        $number_arr = [];
        $cost = [];
        $country_ids = [];
        //Number foreach
        foreach ($all_numbers as $number) {
            //Preparing number
            if (!is_numeric($number)) {
                die('Number not correct');
            }

            if (substr($number, 0, 2) == "05" && strlen($number) == 10) {
                $number = "966" . substr($number, 1);
            } elseif (substr($number, 0, 1) == "5" && strlen($number) == 9) {
                $number = "966" . $number;
            } elseif (substr($number, 0, 4) == "9660" && strlen($number) == 13) {
                $number = str_replace('9660', '966', $number);
            } elseif (substr($number, 0, 5) == "00966" && strlen($number) == 14) {
                $number = str_replace('00966', '966', $number);
            } elseif (substr($number, 0, 4) == "0966" && strlen($number) == 13) {
                $number = str_replace('0966', '966', $number);
            }

            //Get price by country
            foreach ($countries as $key => $price) {
                preg_match("/^{$key}/", substr($number, 0, 5), $match);
                if (!empty($match)) {
                    if ((strlen($number) >= $country_min_number_count[$key]) && (strlen($number) <= $country_max_number_count[$key])) {
                        //Add country id
                        ///add

                        if ($this->input->post_get('user') == "motaz") {
                            // var_dump(!empty($operators[$key]));
                            // die();
                        }
                        $match_status = false;
                        if (!empty($operators[$key])) {
                            foreach ($operators[$key] as $operator) { //Operator foreach
                                $codes = array_filter(explode(",", $operator['code']));
                                foreach ($codes as $code) { //Code foreach
                                    preg_match("/^{$code}/", substr($number, strlen($key), 3), $match_operator);
                                    if (!empty($match_operator)) {
                                        $match_status = true;
                                        $cost[$number] = ($operator['price'] * $message_long);
                                        break;
                                    }
                                } //End code foreach

                                if ($match_status) {
                                    break;
                                }
                            } //End operator foreach
                        }
                        if (!$match_status) {
                            //Add to cost
                            $cost[$number] = ($price * $message_long);
                        }


                        //
                        $country_ids[$number] = $match[0];
                        // $cost[$number] = ($price * $message_long);
                        //Add number 
                        array_push($number_arr, $number);
                        break;
                    }
                }
            } //End country foreach
        } //End number foreach
        //Check number validation
        if (empty($number_arr)) {
            //Check your number. There some problem
            $this->apilog->update_by_array(array('status' => -117, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
            die('-117');
        }

        //Check user balance
        $total_cost = round(array_sum($cost), 1);
        if ((($user_info->total_balance + $user_info->credit_limit) - $user_info->spent_balance) < $total_cost) {
            //Not enought balance
            $this->apilog->update_by_array(array('status' => -113, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
            die('-113');
        }
        //////////////////1/1/2023 mahmoud atif
        /* if($sender){
             $sms_daily = $this->message->get_count_day_by_sender_name($sender->name);
             if($sender->max_sms_one_day != null  && ($total_cost+$sms_daily) > $sender->max_sms_one_day){
                 $this->apilog->update_by_array(array('status'=>-123),array('id'=>$api_log_id));
                 die("-123");
             }
         }*/
        //////////////////1/1/2023 end

        //Start transaction
        $this->db->db_debug = false;
        $this->db->trans_begin();

        //Deduct balance 12-10-2023
        $deduct_balance = $user_info->change_balance((-1 * round($total_cost, 1)), "Send SMS Campaign");
        if (!$deduct_balance) {
            $this->db->trans_rollback();
            //Balance deduct failed
            $this->apilog->update_by_array(array('status' => -118, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
            die('-118');
        }

        //Datetime param handle
        $sending_time = null;
        if (!empty($date)) {
            $sending_time = $date;
            if (!empty($time)) {
                $sending_time = $date . ' ' . $time;
            }

            //Valide date time check
            if (DateTime::createFromFormat('Y-m-d G:i', $sending_time) === false) {
                $sending_time = null;
            }

            //Compare with today
            if (!empty($sending_time)) {
                $send_date = new DateTime($sending_time);
                $to_date = new DateTime();
                if ($send_date < $to_date) {
                    $this->db->trans_rollback();
                    //Selected date not correct
                    $this->apilog->update_by_array(array('status' => -119, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
                    die('-119');
                }
                // if(strpos($sender, '-AD')){
                //     if(strtotime(date("H:i:s",$sending_time)) > strtotime("20:00:00")  || strtotime(date("H:i:s",$sending_time)) < strtotime("09:00:00")){
                //         $this->apilog->update_by_array(array('status'=>-119),array('id'=>$api_log_id));
                //         die('-119');
                //     }
                // }

            }
        }

        //Save to outbox   
        $this->load->model('outbox');
        $auth_code = rand(1000000, 9999999);
        $outbox_param = [
            'id' => null,
            'channel' => 'API',
            'user_id' => $user_info->id,
            'text' => $message,
            'count' => count($number_arr),
            'cost' => $total_cost,
            'creation_datetime' => date("Y-m-d H:i:s"),
            'sending_datetime' => $sending_time,
            'repeation_period' => "0",
            'repeation_times' => 0,
            'variables_message' => 0,
            'sender_name' => $sender_name,
            'all_numbers' => $to,
            'auth_code' => $auth_code,
            'advertising' => $advertising_words
        ];

        $outbox_id = $this->outbox->insert_by_array($outbox_param);
        if (empty($outbox_id)) {
            $this->db->trans_rollback();
            //Insert failed to outbox
            $this->apilog->update_by_array(array('status' => -118, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
            die('-118');
        }

        //Sms processing
        $sms_process = $this->_api_send_sms($outbox_id, $auth_code, 0, $number_arr, $country_ids, $cost, $is_dlr);
        if (!$sms_process['status']) {
            $this->db->trans_rollback();
            die('-118');
        }
        ///////////////////2024/2/13////save short id//////////////////////////////
        if (isset($short_id)) {
            $shortmessages_param = [
                'message_id' => $sms_process['message_id'],
                'short_id' => $short_id
            ];
            $this->db->insert('shortmessagesid', $shortmessages_param);
        }
        ////////////////////////////////////////////////////// 

        //Complete transaction
        $this->db->trans_commit();

        if ($advertising_words) {
            if ($this->site_settings['advertising_message'] == "SMS" || $this->site_settings['advertising_message'] == "BOTH") {
                $this->send_telegram_message($this->lang->line('msg_info_messages_review_msg'));
                // $recive_numbers = explode(",", $this->site_settings['receiver_number_advertising']);
                // foreach ($recive_numbers as $recive_number) {
                //     $this->send_sms_admin(
                //         $this->site_settings['system_sms_sender'],
                //         $recive_number,
                //         strip_tags($this->lang->line('msg_info_messages_review_msg')),
                //         0,
                //         null
                //     );
                // }

            }
        } elseif ($sms_process['type'] == "regular") {
            //Send sms
            $this->curl->_simple_call("get", site_url("user/sms/pre_send_by_auth_code/{$sms_process['message_id']}/{$auth_code}"), array(), array("TIMEOUT" => 3));

        }
        if (!strpos($sender_name, '-AD')) {
            if (count($number_arr) >= $this->site_settings['num_message_regular']) {
                $this->send_telegram_message($this->lang->line('msg_info_messages_overload_telegram'));

                // $this->send_sms_admin(
                //     $this->site_settings['system_sms_sender'],
                //     $this->site_settings['receiver_number'],
                //     strip_tags($this->lang->line('msg_info_messages_overload')),
                //     0,
                //     array('{username}' => $this->session->userdata('user_logged_info')['username'])
                // );
            }
        }

        //orginal response: -999:20031:966521021542
        if ($user_info->is_school == 1) {
            header("Content-type: application/json");
            echo json_encode(['status' => 'Success', 'credits_deducted' => $total_cost]);
        } else {
            $this->apilog->update_by_array(array('status' => 1, 'updated_at' => date('Y-m-d H:i:s')), array('id' => $api_log_id));
            if ($is_dlr == 1) {
                // var_dump($this->messagedetails->get_numbers_key($api_log_id));
                $message_id = $sms_process['message_id'];
                die("Success|$message_id");
            } else {
                die('Success');
            }

        }
    }

    public function shorturl($client_id, $url)
    {
        $data = array(
            'client_id' => $client_id,
            'url' => $url
        );
        $data_string = json_encode($data);
        $curl = curl_init('https://short.dreams.sa/api/links');
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");

        curl_setopt(
            $curl,
            CURLOPT_HTTPHEADER,
            array(
                'Content-Type: application/json',
                'Content-Length: ' . strlen($data_string)
            )
        );
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);  // Make it so the data coming back is put into a string
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);  // Insert the data
        // Send the request
        $result = curl_exec($curl);
        // Free up the resources $curl is using
        curl_close($curl);
        $get_result_arr = json_decode($result, true);
        if ($get_result_arr['status'] != true) {
            dd("The url field must be a valid URL please valid url like https://www.###.##/");
        } else {
            return $get_result_arr;
        }
    }

    protected function meetingUrl($client_id, $summary, $description, $specific_time, $alarm, $alarm_text)
    {
        $data = array(
            'client_id' => $client_id,
            'summary' => $summary,
            'description' => $description,
            'specific_time' => $specific_time,
            'alarm' => $alarm,
            'alarm_text' => $alarm_text,
        );
        $data_string = json_encode($data);
        $curl = curl_init('https://short.dreams.sa/api/links/meeting');
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");

        curl_setopt(
            $curl,
            CURLOPT_HTTPHEADER,
            array(
                'Content-Type: application/json',
                'Content-Length: ' . strlen($data_string)
            )
        );
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);  // Make it so the data coming back is put into a string
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);  // Insert the data
        // Send the request
        $result = curl_exec($curl);
        // Free up the resources $curl is using
        curl_close($curl);
        $get_result_arr = json_decode($result, true);

        return $get_result_arr;
    }

    private function send_error_response($error_code, $api_log_id = null, $error_message = null, $json_data = null,$username = null)
    {
        $error_messages = array(
            '-100' => 'Required parameters missing',
            '-110' => 'Invalid username or password',
            '-111' => 'User is not active',
            '-112' => 'User is blocked',
            '-113' => 'Insufficient balance',
            '-114' => 'Site sending case not found',
            '-115' => 'Sender not found or not authorized',
            '-116' => 'Invalid sender name format',
            '-117' => 'Invalid number format',
            '-118' => 'Processing error',
            '-119' => 'Invalid date/time',
            '-120' => 'Invalid username format',
            '-121' => 'Invalid password',
            '-122' => 'No coverage for this number',
            '-123' => 'Daily SMS limit exceeded',
            '-124' => 'IP not whitelisted'
        );
        $message = $error_message ?: ($error_messages[$error_code] ?? 'Unknown error');
       
        if ($api_log_id) {
            $this->apilog->update_by_array(
                array('status' => $error_code, 'updated_at' => date('Y-m-d H:i:s'),'username'=>$username,'text'=>$message),
                array('id' => $api_log_id)
            );
        }else{
            $this->apilog->update_by_array(
                array('status' => $error_code, 'updated_at' => date('Y-m-d H:i:s'),'username'=>$username,'text'=>$message),
                array('id' => -1)
            );
        }

        // Check if XML response is requested
        $format = $this->input->post_get('format') ?: 'json';
        $wants_xml = strtolower($format) === 'xml' ||
            strpos($this->input->get_request_header('Accept'), 'application/xml') !== false ||
            strpos($this->input->get_request_header('Accept'), 'text/xml') !== false;


      
        $response_data = array(
            'status' => 'Error',
            'code' => $error_code,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s'),
            'request_id' => $api_log_id
        );


        if ($wants_xml) {
            header('Content-Type: application/xml; charset=utf-8');
            $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><response></response>');
            $this->array_to_xml($response_data, $xml);
            die($xml->asXML());
        } else {
            if ($json_data) {
                header('Content-Type: application/json; charset=utf-8');
                die(json_encode($response_data));
            }
        }
        die($error_code);
    }

    private function send_success_response($response_data, $api_log_id = null, $is_school = 0, $is_dlr = 0, $is_balance = 0, $is_user_sender = 0, $success_code = 0)
    {
        if ($api_log_id) {
            $this->apilog->update_by_array(
                array('status' => 1, 'updated_at' => date('Y-m-d H:i:s')),
                array('id' => $api_log_id)
            );
        }
        if ($is_school === 1) {
            header("Content-type: application/json");
            echo json_encode(['status' => 'Success', 'credits_deducted' => $response_data['credits_deducted']]);
            die();
        }
        $wants_xml = false;
        if (
            strtolower($this->input->post_get('response')) === 'xml' ||
            strpos($this->input->get_request_header('Accept'), 'application/xml') !== false ||
            strpos($this->input->get_request_header('Accept'), 'text/xml') !== false
        ) {
            $wants_xml = true;
        }
        if ($wants_xml) {
            header('Content-Type: application/xml; charset=utf-8');
            $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><response></response>');
            $this->array_to_xml($response_data, $xml);
            die($xml->asXML());
        }
        if ($is_dlr) {
            $message_id = $response_data['message_id'];
            die("Success|$message_id");
        }
        if ($is_balance) {
            $balance = $response_data['balance'];
            die("$balance");
        }
        if ($is_user_sender) {
            header('Content-Type: application/json');
            echo json_encode($response_data);
            die();
        }
        if ($success_code <> 0) {
            die("$success_code");
        }
        die('Success');
    }
    private function send_success_responseV2($response_data, $api_log_id = null, $is_school = 0, $is_dlr = 0, $success_code = 0, $json_response = 0)
    {
        if ($api_log_id) {
            $this->apilog->update_by_array(
                array('status' => 1, 'updated_at' => date('Y-m-d H:i:s')),
                array('id' => $api_log_id)
            );
        }
        // if ($is_school === 1) {
        //     header("Content-type: application/json");
        //     echo json_encode(['status' => 'Success', 'credits_deducted' => $response_data['credits_deducted']]);
        //     die();
        // }
        $format = $this->input->post_get('format') ?: 'json';
        $wants_xml = strtolower($format) === 'xml' ||
            strpos($this->input->get_request_header('Accept'), 'application/xml') !== false ||
            strpos($this->input->get_request_header('Accept'), 'text/xml') !== false;
        $response_data['code'] = $success_code;
        if ($success_code == 0) {
            $response_data['code'] = 200;
        }
        $response_data['message'] = 'Success';

        if ($wants_xml) {
            header('Content-Type: application/xml; charset=utf-8');
            $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><response></response>');
            $this->array_to_xml($response_data, $xml);
            die($xml->asXML());
        }

        if ($json_response) {
            header('Content-Type: application/json; charset=utf-8');
            die(json_encode($response_data));
        }
        if ($is_dlr) {
            $message_id = $response_data['message_id'];
            die("Success|$message_id");
        }
        die('Success');
    }
    private function array_to_xml($data, &$xml)
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                if (is_numeric($key)) {
                    $key = 'sender';
                }
                $subnode = $xml->addChild($key);
                $this->array_to_xml($value, $subnode);
            } else {
                $xml->addChild($key, htmlspecialchars((string) $value));
            }
        }
    }

    public function check_server()
    {
        header('Content-Type: application/json');

        // TODO: check databese connection status 


        // server info
        // Read /proc/1/stat to get the start time
        $statContent = file_get_contents('/proc/1/stat');
        $statParts = explode(' ', $statContent);
        $startTimeTicks = $statParts[21];

        // Get the system clock ticks per second
        $clockTicks = (int) shell_exec('getconf CLK_TCK');

        // Calculate the start time in seconds since boot
        $startTimeSeconds = $startTimeTicks / $clockTicks;

        // Get the system uptime
        $uptimeString = file_get_contents('/proc/uptime');
        list($uptimeSeconds) = explode(' ', $uptimeString);

        // Calculate the actual start time by subtracting process start time from uptime
        $currentTime = time();
        $bootTime = $currentTime - (int) $uptimeSeconds;
        $startTime = $bootTime + $startTimeSeconds;

        // Format times as readable strings
        $currentTimeFormatted = date('Y-m-d H:i:s', $currentTime);
        $startTimeFormatted = date('Y-m-d H:i:s', $startTime);
        $uptime = $currentTime - $startTime;

        $nodeuptime = shell_exec('uptime -p');

        $serverInfo = [
            'hostname' => gethostname(),
            'current_time' => $currentTimeFormatted,
            'start_time' => $startTimeFormatted,
            'uptime_seconds' => $uptime,
            'uptime' => gmdate('H:i:s', $uptime),
            'node-uptime' => $nodeuptime
        ];

        // version information
        $versionFile = 'version.txt';
        if (file_exists($versionFile)) {
            $lines = file($versionFile, FILE_IGNORE_NEW_LINES);
            if (count($lines) >= 2) {
                $version_info = [
                    'version' => $lines[1],
                    'branch' => $lines[0]
                ];
            }
        } else {
            $version_info = [
                'version' => 'unknown',
                'branch' => 'unknown'
            ];
        }
        $healthy = $uptimeSeconds > 30; // && $databaseHealthy

        $response = [
            'status' => $healthy ? 'healthy' : 'unhealthy',
            'server_info' => $serverInfo,
            'version_info' => $version_info
        ];
        http_response_code($healthy ? 200 : 503);
        echo json_encode($response);
    }

    public function check_ip()
    {
        $user = $this->user_details;
        $delete_prv_ip = $this->input->post_get('delete_prv_ip');
        $this->load->service('IpWhitelist');
        $verify_ip = $this->IpWhitelist->verify_ip($user);
        if (!$verify_ip) {
            $this->IpWhitelist->add_ip_from_service($user, $delete_prv_ip);
        }

    }
}