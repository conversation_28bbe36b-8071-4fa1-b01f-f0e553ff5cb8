<?php
defined('BASEPATH') or exit('No direct script access allowed');

class AdminNotificationService {
    protected $CI;
    private $admin_phone;

    public function __construct() {
        $this->CI =& get_instance();
        $this->CI->load->model(['setting', 'user','messagedetails','message']);
        $this->admin_phone = $this->CI->setting->get_value_by_name('admin_phone_number');
    }

    /**
     * Send notification to admin for advertising SMS
     * 
     * @return bool
     */
    public function notifyAdminForAdvertising() {
        try {
            if ( $this->CI->setting->get_value_by_name('advertising_message')  == "SMS" || $this->CI->setting->get_value_by_name('advertising_message') == "BOTH") {
                $admin_phones = explode(",", $this->CI->setting->get_value_by_name('receiver_number_advertising') );
                foreach ($admin_phones as $admin_phone) {
                    $this->send_sms_admin(
                        $this->CI->setting->get_value_by_name('system_sms_sender'),
                        $admin_phone,
                        strip_tags($this->CI->lang->line('msg_info_messages_review_msg')),
                        0,
                        null
                    );
                }
               
            }
        } catch (Exception $e) {
            log_message('error', 'Failed to send admin notification: ' . $e->getMessage());
            return false;
        }
        return true;
    }

     public function notifyAdminForError() {
        try {
            if ( $this->CI->setting->get_value_by_name('advertising_message')  == "SMS" || $this->CI->setting->get_value_by_name('advertising_message') == "BOTH") {
                $admin_phones = explode(",", $this->CI->setting->get_value_by_name('receiver_number_advertising') );
                foreach ($admin_phones as $admin_phone) {
                    $this->send_sms_admin(
                        $this->CI->setting->get_value_by_name('system_sms_sender'),
                        $admin_phone,
                        strip_tags($this->CI->lang->line('msg_info_messages_review_msg')),
                        0,
                        null
                    );
                }
               
            }
        } catch (Exception $e) {
            log_message('error', 'Failed to send admin notification: ' . $e->getMessage());
            return false;
        }
        return true;
    }


    public function send_sms_admin($sender_name, $all_numbers, $message, $gateway_id = 0, $array_params = null)
    {
      
        $unique_suffix = rand(1000000, 9999999);

        $this->CI->message->admin();

        $this->CI->load->model('messagedetails');
        if (!empty($array_params)) {
            $param_names = array_keys($array_params);
            $param_values = array_values($array_params);
            $message = str_replace($param_names, $param_values, $message);
        }

        $this->CI->messagedetails->create_statistics_temp_table($unique_suffix, $message, $all_numbers, false, '', 0);
        $statistics = $this->CI->messagedetails->get_statistics($unique_suffix);

        $total_cnt = 0;
        $total_cost = 0;
        foreach ($statistics as $entry) {
            if ($entry['coverage_status'] == 1) {
                $total_cost += $entry["cost"];
                $total_cnt += $entry["cnt"];
            }
        }
        if ($total_cnt > 0) {
            $auth_code = rand(1000000, 9999999);
            $user_id = $this->CI->session->userdata('admin_logged_info')['id'];
            //dd($this->session->userdata('admin_logged_info'));
            $message_id = $this->CI->message->insert_by_array(
                array(
                    'user_id' => empty($user_id) ? 0 : $user_id,
                    'text' => $message,
                    'creation_datetime' => date("Y-m-d H:i:s"),
                    'sending_datetime' => null,
                    'variables_message' => 0,
                    'count' => $total_cnt,
                    'cost' => round($total_cost, 1),
                    'sender_name' => $sender_name,
                    'auth_code' => $auth_code
                )
            );

            $this->CI->messagedetails->admin();
            $this->CI->messagedetails->save_statistics_temp_table($message_id, $unique_suffix);
            $this->CI->messagedetails->drop_statistics_temp_table($unique_suffix);
            $this->CI->load->library('curl');
            $this->CI->curl->_simple_call("get", site_url("admin/sms/send_by_auth_code/{$message_id}/{$auth_code}/{$gateway_id}"), array(), array("TIMEOUT" => 1));
        }
    }
}